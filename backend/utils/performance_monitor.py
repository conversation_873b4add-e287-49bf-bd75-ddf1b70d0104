"""
性能监控模块

此模块提供性能监控和分析功能，用于收集API响应时间和资源使用数据。
主要功能包括：
1. API响应时间监控
2. 资源使用监控（CPU、内存）
3. LLM调用性能统计
4. 数据库查询性能统计
5. 性能数据可视化
"""

import time
import logging
import psutil
import threading
import functools
import json
import os
from typing import Dict, Any
from datetime import datetime
import asyncio
from contextlib import contextmanager
from collections import deque

# 设置日志记录器
logger = logging.getLogger(__name__)

class PerformanceMetric:
    """性能指标基类"""
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.created_at = datetime.now()
        self.last_updated = self.created_at

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "description": self.description,
            "created_at": self.created_at.isoformat(),
            "last_updated": self.last_updated.isoformat()
        }

class TimingMetric(PerformanceMetric):
    """时间指标"""
    def __init__(self, name: str, description: str = ""):
        super().__init__(name, description)
        self.count = 0
        self.total_time = 0.0
        self.min_time = float('inf')
        self.max_time = 0.0
        self.recent_times = deque(maxlen=100)  # 保存最近100次调用的时间

    def record(self, elapsed_time: float):
        """记录一次调用的耗时"""
        self.count += 1
        self.total_time += elapsed_time
        self.min_time = min(self.min_time, elapsed_time)
        self.max_time = max(self.max_time, elapsed_time)
        self.recent_times.append(elapsed_time)
        self.last_updated = datetime.now()

    def get_avg_time(self) -> float:
        """获取平均耗时"""
        if self.count == 0:
            return 0.0
        return self.total_time / self.count

    def get_recent_avg_time(self) -> float:
        """获取最近调用的平均耗时"""
        if not self.recent_times:
            return 0.0
        return sum(self.recent_times) / len(self.recent_times)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        base_dict = super().to_dict()
        base_dict.update({
            "count": self.count,
            "total_time": self.total_time,
            "min_time": self.min_time if self.min_time != float('inf') else 0.0,
            "max_time": self.max_time,
            "avg_time": self.get_avg_time(),
            "recent_avg_time": self.get_recent_avg_time()
        })
        return base_dict

class ResourceMetric(PerformanceMetric):
    """资源使用指标"""
    def __init__(self, name: str, description: str = ""):
        super().__init__(name, description)
        self.cpu_usage = []
        self.memory_usage = []
        self.timestamps = []
        self.sampling_interval = 60  # 默认采样间隔（秒）

    def record(self, cpu_percent: float, memory_percent: float):
        """记录资源使用情况"""
        self.cpu_usage.append(cpu_percent)
        self.memory_usage.append(memory_percent)
        self.timestamps.append(datetime.now())

        # 只保留最近一段时间的数据
        if len(self.timestamps) > 1000:  # 限制数据点数量
            self.cpu_usage.pop(0)
            self.memory_usage.pop(0)
            self.timestamps.pop(0)

        self.last_updated = datetime.now()

    def get_avg_cpu(self) -> float:
        """获取平均CPU使用率"""
        if not self.cpu_usage:
            return 0.0
        return sum(self.cpu_usage) / len(self.cpu_usage)

    def get_avg_memory(self) -> float:
        """获取平均内存使用率"""
        if not self.memory_usage:
            return 0.0
        return sum(self.memory_usage) / len(self.memory_usage)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        base_dict = super().to_dict()
        base_dict.update({
            "avg_cpu": self.get_avg_cpu(),
            "avg_memory": self.get_avg_memory(),
            "current_cpu": self.cpu_usage[-1] if self.cpu_usage else 0.0,
            "current_memory": self.memory_usage[-1] if self.memory_usage else 0.0,
            "samples_count": len(self.timestamps)
        })
        return base_dict

class PerformanceMonitor:
    """性能监控器"""
    _instance = None

    def __new__(cls, *args, **kwargs):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super(PerformanceMonitor, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, data_dir: str = None,
                 enabled: bool = True,
                 auto_save: bool = True,
                 save_interval: int = 300):
        """
        初始化性能监控器

        Args:
            data_dir: 性能数据保存目录
            enabled: 是否启用性能监控
            auto_save: 是否自动保存性能数据
            save_interval: 自动保存间隔（秒）
        """
        if self._initialized:
            return

        self.data_dir = data_dir
        self.enabled = enabled
        self.auto_save = auto_save
        self.save_interval = save_interval

        # 只有在启用时才创建数据目录
        if self.enabled and data_dir:
            try:
                os.makedirs(data_dir, exist_ok=True)
                logger.info(f"性能监控数据目录已创建: {data_dir}")
            except Exception as e:
                logger.error(f"创建性能监控数据目录失败: {str(e)}")
                # 尝试使用备用目录（在logs目录下）
                backup_dir = "logs/performance_backup"
                try:
                    os.makedirs(backup_dir, exist_ok=True)
                    self.data_dir = backup_dir
                    logger.warning(f"使用备用数据目录: {backup_dir}")
                except Exception as backup_error:
                    logger.error(f"创建备用目录也失败: {str(backup_error)}")
                    self.enabled = False
                    logger.warning("性能监控已禁用，因为无法创建数据目录")
        elif self.enabled and not data_dir:
            logger.warning("性能监控启用但未指定数据目录，将使用默认性能日志目录")
            # 使用默认的性能日志目录
            default_dir = "logs/performance"
            try:
                os.makedirs(default_dir, exist_ok=True)
                self.data_dir = default_dir
                logger.info(f"使用默认性能日志目录: {default_dir}")
            except Exception as default_error:
                logger.error(f"创建默认性能日志目录失败: {str(default_error)}")
                # 如果默认目录创建失败，才使用备用目录
                backup_dir = "logs/performance_backup"
                try:
                    os.makedirs(backup_dir, exist_ok=True)
                    self.data_dir = backup_dir
                    logger.warning(f"使用备用数据目录: {backup_dir}")
                except Exception as backup_error:
                    logger.error(f"创建备用目录也失败: {str(backup_error)}")
                    self.enabled = False
                    logger.warning("性能监控已禁用，因为无法创建数据目录")

        # 初始化指标集合
        self.api_metrics = {}  # API响应时间指标
        self.llm_metrics = {}  # LLM调用性能指标
        self.db_metrics = {}   # 数据库查询性能指标
        self.resource_metric = ResourceMetric("system_resources", "系统资源使用情况")

        # 启动资源监控线程
        self.stop_monitoring = False
        if self.enabled:
            self.monitor_thread = threading.Thread(target=self._monitor_resources, daemon=True)
            self.monitor_thread.start()

            # 启动自动保存线程
            if self.auto_save:
                self.save_thread = threading.Thread(target=self._auto_save, daemon=True)
                self.save_thread.start()

        self._initialized = True
        logger.info("性能监控器初始化完成")

    def _monitor_resources(self):
        """资源监控线程"""
        # 设置采样间隔为10秒
        sampling_interval = 10

        while not self.stop_monitoring:
            try:
                # 获取系统资源使用情况
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_percent = psutil.virtual_memory().percent

                # 记录资源使用情况
                self.resource_metric.record(cpu_percent, memory_percent)

                # 休眠一段时间
                time.sleep(sampling_interval - 1)  # 减去获取CPU使用率的1秒
            except Exception as e:
                logger.error(f"资源监控出错: {str(e)}")
                time.sleep(30)  # 出错后等待30秒再重试

    def _auto_save(self):
        """自动保存线程"""
        while not self.stop_monitoring:
            try:
                time.sleep(self.save_interval)
                self.save_metrics()
            except Exception as e:
                logger.error(f"自动保存性能指标出错: {str(e)}")

    def track_api_call(self, endpoint: str):
        """
        跟踪API调用性能的装饰器

        Args:
            endpoint: API端点名称
        """
        def decorator(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                if not self.enabled:
                    return await func(*args, **kwargs)

                # 获取或创建指标
                if endpoint not in self.api_metrics:
                    self.api_metrics[endpoint] = TimingMetric(
                        f"api_{endpoint}", f"API端点 {endpoint} 的响应时间"
                    )

                # 记录开始时间
                start_time = time.time()

                try:
                    # 调用原函数
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    # 记录耗时
                    elapsed_time = time.time() - start_time
                    self.api_metrics[endpoint].record(elapsed_time)

            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                if not self.enabled:
                    return func(*args, **kwargs)

                # 获取或创建指标
                if endpoint not in self.api_metrics:
                    self.api_metrics[endpoint] = TimingMetric(
                        f"api_{endpoint}", f"API端点 {endpoint} 的响应时间"
                    )

                # 记录开始时间
                start_time = time.time()

                try:
                    # 调用原函数
                    result = func(*args, **kwargs)
                    return result
                finally:
                    # 记录耗时
                    elapsed_time = time.time() - start_time
                    self.api_metrics[endpoint].record(elapsed_time)

            # 根据函数类型返回不同的包装器
            if asyncio.iscoroutinefunction(func):
                return async_wrapper
            return sync_wrapper

        return decorator

    @contextmanager
    def track_llm_call(self, provider: str, model: str, operation: str):
        """
        跟踪LLM调用性能的上下文管理器

        Args:
            provider: 提供商名称
            model: 模型名称
            operation: 操作类型
        """
        if not self.enabled:
            yield
            return

        # 创建指标键
        metric_key = f"{provider}_{model}_{operation}"

        # 获取或创建指标
        if metric_key not in self.llm_metrics:
            self.llm_metrics[metric_key] = TimingMetric(
                metric_key, f"{provider} {model} 的 {operation} 操作响应时间"
            )

        # 记录开始时间
        start_time = time.time()

        try:
            # 执行被跟踪的代码
            yield
        finally:
            # 记录耗时
            elapsed_time = time.time() - start_time
            self.llm_metrics[metric_key].record(elapsed_time)

    @contextmanager
    def track_db_query(self, query_type: str, table: str = None):
        """
        跟踪数据库查询性能的上下文管理器

        Args:
            query_type: 查询类型（select, insert, update, delete等）
            table: 表名
        """
        if not self.enabled:
            yield
            return

        # 创建指标键
        metric_key = f"{query_type}"
        if table:
            metric_key = f"{query_type}_{table}"

        # 获取或创建指标
        if metric_key not in self.db_metrics:
            self.db_metrics[metric_key] = TimingMetric(
                metric_key, f"{query_type} 查询" + (f"（表：{table}）" if table else "")
            )

        # 记录开始时间
        start_time = time.time()

        try:
            # 执行被跟踪的代码
            yield
        finally:
            # 记录耗时
            elapsed_time = time.time() - start_time
            self.db_metrics[metric_key].record(elapsed_time)

    def get_api_metrics(self) -> Dict[str, Dict[str, Any]]:
        """获取API指标"""
        return {name: metric.to_dict() for name, metric in self.api_metrics.items()}

    def get_llm_metrics(self) -> Dict[str, Dict[str, Any]]:
        """获取LLM指标"""
        return {name: metric.to_dict() for name, metric in self.llm_metrics.items()}

    def get_db_metrics(self) -> Dict[str, Dict[str, Any]]:
        """获取数据库指标"""
        return {name: metric.to_dict() for name, metric in self.db_metrics.items()}

    def get_resource_metrics(self) -> Dict[str, Any]:
        """获取资源指标"""
        return self.resource_metric.to_dict()

    def get_all_metrics(self) -> Dict[str, Any]:
        """获取所有指标"""
        return {
            "api": self.get_api_metrics(),
            "llm": self.get_llm_metrics(),
            "db": self.get_db_metrics(),
            "resources": self.get_resource_metrics(),
            "timestamp": datetime.now().isoformat()
        }

    def save_metrics(self, filename: str = None) -> str:
        """
        保存性能指标到文件

        Args:
            filename: 文件名，如果为None则使用当前时间戳

        Returns:
            str: 保存的文件路径
        """
        try:
            # 确保数据目录存在并使用绝对路径
            if not os.path.isabs(self.data_dir):
                # 获取项目根目录
                project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
                data_dir = os.path.join(project_root, self.data_dir)
            else:
                data_dir = self.data_dir

            os.makedirs(data_dir, exist_ok=True)

            if filename is None:
                filename = f"performance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            file_path = os.path.join(data_dir, filename)

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.get_all_metrics(), f, ensure_ascii=False, indent=2)

            logger.info(f"性能指标已保存到 {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"保存性能指标失败: {str(e)}")
            # 尝试保存到备用位置（在logs目录下）
            try:
                backup_dir = "logs/performance_backup"
                os.makedirs(backup_dir, exist_ok=True)
                backup_filename = filename or f"performance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                backup_path = os.path.join(backup_dir, backup_filename)

                with open(backup_path, 'w', encoding='utf-8') as f:
                    json.dump(self.get_all_metrics(), f, ensure_ascii=False, indent=2)

                logger.warning(f"性能指标已保存到备用位置: {backup_path}")
                return backup_path

            except Exception as backup_error:
                logger.error(f"备用保存也失败: {str(backup_error)}")
                return ""

    def reset_metrics(self):
        """重置所有指标"""
        self.api_metrics = {}
        self.llm_metrics = {}
        self.db_metrics = {}
        self.resource_metric = ResourceMetric("system_resources", "系统资源使用情况")
        logger.info("所有性能指标已重置")

    def shutdown(self):
        """关闭性能监控器"""
        self.stop_monitoring = True
        if self.enabled:
            # 从配置获取线程等待超时时间
            try:
                from backend.config.unified_config_loader import get_unified_config
                unified_config = get_unified_config()
                thread_timeout = unified_config.get_threshold("performance.timeout.short_operation", 2)
            except Exception:
                thread_timeout = 2

            if hasattr(self, 'monitor_thread'):
                self.monitor_thread.join(timeout=thread_timeout)
            if self.auto_save and hasattr(self, 'save_thread'):
                self.save_thread.join(timeout=thread_timeout)
        logger.info("性能监控器已关闭")

# 创建全局实例
performance_monitor = PerformanceMonitor(
    data_dir="logs/performance",  # 使用正确的性能日志目录
    enabled=True  # 重新启用性能监控
)
