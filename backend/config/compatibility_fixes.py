#!/usr/bin/env python3
"""
配置兼容性修复

为了确保新旧配置系统完全兼容，提供特殊的兼容性处理
"""

from typing import Any, Dict
from dataclasses import dataclass


@dataclass
class LLMConfig:
    """LLM配置兼容性类"""
    model: str = "gpt-3.5-turbo"
    temperature: float = 0.7
    max_tokens: int = 1000
    timeout: int = 10
    description: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "model": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "timeout": self.timeout,
            "description": self.description
        }


def convert_dict_to_llm_config(config_dict: Dict[str, Any]) -> LLMConfig:
    """将字典转换为LLMConfig对象"""
    return LLMConfig(
        model=config_dict.get("model", "gpt-3.5-turbo"),
        temperature=config_dict.get("temperature", 0.7),
        max_tokens=config_dict.get("max_tokens", 1000),
        timeout=config_dict.get("timeout", 10),
        description=config_dict.get("description", "")
    )


def normalize_message_template_path(template_path: str) -> str:
    """标准化消息模板路径"""
    # 路径映射表
    path_mappings = {
        "greeting.basic": "greeting.basic",
        "error.general": "error.general_error",
        "clarification.request": "clarification.request",
        "confirmation.document": "confirmation.document_confirmation"
    }
    
    return path_mappings.get(template_path, template_path)


def extract_nested_config(config_data: Dict[str, Any], path: str, default: Any = None) -> Any:
    """从嵌套配置中提取值"""
    keys = path.split('.')
    current = config_data
    
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return default
    
    return current
