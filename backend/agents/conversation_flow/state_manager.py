"""
对话流程状态管理器

负责管理：
- 关注点（Focus Points）状态
- 会话状态缓存
- 状态转换逻辑
"""

import json
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime

from backend.config.unified_config_loader import get_unified_config


class StateManager:
    """状态管理器"""

    # 状态常量
    STATUS_PENDING = "pending"
    STATUS_PROCESSING = "processing"
    STATUS_COMPLETED = "completed"

    # 优先级常量
    PRIORITY_P0 = "P0"
    PRIORITY_P1 = "P1"
    PRIORITY_P2 = "P2"

    def __init__(self, db_manager, focus_point_manager):
        self.db_manager = db_manager
        self.focus_point_manager = focus_point_manager
        self.focus_points_status_cache = {}
        self.logger = logging.getLogger(__name__)

        # 缓存配置，避免重复调用
        self.config = get_unified_config()

        # 缓存优先级配置
        self._cache_priority_config()

    def _cache_priority_config(self):
        """缓存优先级配置，避免重复调用"""
        self.p0_required = self.config.get_business_rule("business_rules.focus_point_priority.p0", True)
        self.p1_required = self.config.get_business_rule("business_rules.focus_point_priority.p1", True)
        self.p2_required = self.config.get_business_rule("business_rules.focus_point_priority.p2", False)

        # 优先级映射
        self.priority_map = {
            self.PRIORITY_P0: 0,
            self.PRIORITY_P1: 1,
            self.PRIORITY_P2: 2
        }

    def _should_process_priority(self, priority: str) -> bool:
        """判断是否需要处理指定优先级的关注点"""
        priority = priority.upper()
        if priority == self.PRIORITY_P0:
            return self.p0_required
        elif priority == self.PRIORITY_P1:
            return self.p1_required
        elif priority == self.PRIORITY_P2:
            return self.p2_required
        return False

    async def initialize_focus_points(self, session_id: str, user_id: str, focus_points: List[Dict[str, Any]]) -> bool:
        """
        初始化关注点状态 - 直接使用FocusPointManager

        Args:
            session_id: 会话ID
            user_id: 用户ID
            focus_points: 关注点列表

        Returns:
            bool: 初始化是否成功
        """
        return await self.focus_point_manager.initialize_focus_points(session_id, user_id, focus_points)

    async def update_focus_point_status(self, session_id: str, user_id: str, point_id: str, status: str, value: Optional[str] = None) -> bool:
        """
        更新关注点状态 - 直接使用FocusPointManager并同步更新缓存

        Args:
            session_id: 会话ID
            user_id: 用户ID
            point_id: 关注点ID
            status: 新状态
            value: 可选的值

        Returns:
            bool: 更新是否成功
        """
        # 确保value是字符串类型，避免数据库绑定错误
        if value is not None:
            if isinstance(value, dict):
                value = json.dumps(value, ensure_ascii=False)
            elif not isinstance(value, str):
                value = str(value)

        # 更新数据库
        success = await self.focus_point_manager.update_focus_point_status(session_id, user_id, point_id, status, value)

        # 如果数据库更新成功，同步更新缓存以保持一致性
        if success:
            self.focus_points_status_cache[point_id] = {
                "status": status,
                "value": value,
                "attempts": 0,
                "is_covered": status == self.STATUS_COMPLETED,
                "updated_at": datetime.now().isoformat()
            }

        return success

    async def get_next_pending_point(self, session_id: str, user_id: str, focus_points: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        获取下一个待处理的关注点（根据优先级配置）

        Args:
            session_id: 会话ID
            user_id: 用户ID
            focus_points: 关注点列表

        Returns:
            Optional[Dict[str, Any]]: 下一个待处理的关注点，如果没有则返回None
        """
        if not session_id:
            return None

        # 强制重新加载最新状态，确保状态同步
        statuses = await self.focus_point_manager.load_focus_points_status(session_id, user_id)
        self.logger.debug(f"get_next_pending_point - 重新加载状态数量: {len(statuses)}")

        # 按优先级和ID排序，确保稳定的选择顺序
        sorted_points = sorted(focus_points, key=lambda x: (self.priority_map.get(x["priority"], 3), x["id"]))

        # 根据配置决定需要处理的关注点
        for point in sorted_points:
            point_id = point["id"]
            priority = point.get("priority", self.PRIORITY_P0).upper()
            status = statuses.get(point_id, {}).get("status", self.STATUS_PENDING)

            self.logger.debug(f"检查关注点 {point_id} ({point.get('name', 'Unknown')}) - 状态: {status}, 优先级: {priority}")

            # 使用提取的方法判断是否需要处理该优先级的关注点
            should_process = self._should_process_priority(priority)

            if status == self.STATUS_PENDING and should_process:
                self.logger.info(f"选择关注点: {point_id} ({point.get('name', 'Unknown')}) - 优先级: {priority}")
                return point

        self.logger.info("没有找到待处理的关注点")
        return None

    async def get_processing_point(self, session_id: str, user_id: str) -> Optional[str]:
        """
        获取当前正在处理的关注点ID

        Returns:
            Optional[str]: 正在处理的关注点ID，如果没有则返回None
        """
        if not session_id:
            return None

        statuses = await self.focus_point_manager.load_focus_points_status(session_id, user_id)
        self.logger.debug(f"get_processing_point - 加载的状态数量: {len(statuses)}")

        processing_points = []
        for point_id, status_info in statuses.items():
            current_status = status_info.get("status")
            self.logger.debug(f"关注点 {point_id} 状态: {current_status}")
            if current_status == self.STATUS_PROCESSING:
                processing_points.append(point_id)

        if processing_points:
            if len(processing_points) > 1:
                self.logger.warning(f"发现多个processing状态的关注点: {processing_points}，返回第一个")
            self.logger.info(f"找到processing关注点: {processing_points[0]}")
            return processing_points[0]

        self.logger.debug("未找到processing状态的关注点")
        return None

    async def clear_all_processing_status(self, session_id: str, user_id: str) -> None:
        """
        清理所有processing状态的关注点

        将所有处于processing状态的关注点重置为pending状态
        """
        if not session_id:
            return

        try:
            # 使用高效的批量更新查询，一次性清理所有processing状态
            await self.db_manager.execute_update(
                self.config.get_database_query("focus_points.clear_processing"),
                (session_id, user_id)
            )
            self.logger.debug(f"清理所有processing状态完成 - session_id: {session_id}, user_id: {user_id}")
        except Exception as e:
            self.logger.error(f"清理processing状态失败: {e}", exc_info=True)

    async def set_point_processing(self, session_id: str, user_id: str, point_id: str) -> bool:
        """
        安全地设置关注点为processing状态

        Args:
            session_id: 会话ID
            user_id: 用户ID
            point_id: 关注点ID

        Returns:
            bool: 设置是否成功
        """
        await self.clear_all_processing_status(session_id, user_id)
        return await self.update_focus_point_status(session_id, user_id, point_id, self.STATUS_PROCESSING)

    async def set_point_processing_safely(self, session_id: str, user_id: str, point_id: str) -> bool:
        """
        安全设置processing状态（别名方法，保持与文档一致）

        Args:
            session_id: 会话ID
            user_id: 用户ID
            point_id: 关注点ID

        Returns:
            bool: 设置是否成功
        """
        return await self.set_point_processing(session_id, user_id, point_id)

    async def check_all_required_covered(self, session_id: str, user_id: str, focus_points: List[Dict[str, Any]]) -> bool:
        """
        检查所有必需的关注点是否全部完成（根据优先级配置）

        Args:
            session_id: 会话ID
            user_id: 用户ID
            focus_points: 关注点列表

        Returns:
            bool: 所有必需关注点是否都已完成
        """
        if not focus_points:
            return False

        statuses = await self.focus_point_manager.load_focus_points_status(session_id, user_id)

        # 检查所有必需级别的关注点是否都已完成
        for point in focus_points:
            priority = point.get("priority", self.PRIORITY_P0).upper()
            point_id = point["id"]
            status_info = statuses.get(point_id, {})
            status = status_info.get("status", self.STATUS_PENDING)

            # 使用提取的方法判断是否需要检查该优先级的关注点
            should_check = self._should_process_priority(priority)

            # 如果需要检查且未完成，则返回False
            if should_check and status != self.STATUS_COMPLETED:
                self.logger.info(f"关注点 {point_id} (优先级: {priority}) 未完成，状态: {status}")
                return False

        self.logger.info("所有必需的关注点都已完成")
        return True

    def get_focus_point_status(self, point_id: str) -> Dict[str, Any]:
        """
        获取关注点状态 - 从缓存返回

        Args:
            point_id: 关注点ID

        Returns:
            Dict[str, Any]: 关注点状态信息
        """
        return self.focus_points_status_cache.get(point_id, {})

    async def reset_focus_points(self, session_id: str, user_id: str) -> None:
        """
        重置关注点状态

        Args:
            session_id: 会话ID
            user_id: 用户ID
        """
        await self.focus_point_manager.reset_focus_points_status(session_id, user_id)
        self.focus_points_status_cache = {}
