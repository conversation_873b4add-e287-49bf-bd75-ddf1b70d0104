# ============================================================================
# 默认配置文件 (unified_config.defaults.yaml) - 硬编码托底配置
# ============================================================================
#
# 作用：提供所有硬编码值的默认配置，作为系统的托底保障
# 
# 设计原则：
# 1. 托底保障：即使外部配置缺失，系统也能正常运行
# 2. 安全默认：所有默认值都是安全和合理的
# 3. 完整覆盖：涵盖所有可能的硬编码场景
# 4. 易于维护：清晰的分类和注释
#
# 加载优先级：
# 1. unified_config.defaults.yaml (本文件) - 最低优先级，托底保障
# 2. unified_config.yaml - 环境特定配置，覆盖默认值
# 3. 环境变量 - 最高优先级，运行时覆盖
#
# ⚠️ 重要说明：
# - 本文件应该纳入版本控制，与代码一同发布
# - 修改本文件需要重新部署应用
# - 本文件加载失败应视为严重错误，系统应拒绝启动
# ============================================================================

# ============================================================================
# 系统基础默认配置
# ============================================================================
system:
  version: "3.0"
  description: "需求采集系统默认配置"
  language: "zh-CN"
  supported_languages: ["zh-CN", "en-US"]
  fallback_enabled: true
  debug_mode: false

  logging:
    level: "INFO"
    format: "json"
    max_file_size: "10MB"
    backup_count: 5

  decision_engine:
    type: "simplified"
    enable_caching: true
    cache_ttl: 300
    fallback_to_simplified: true

  performance:
    llm_timeout: 10
    max_retry_attempts: 3
    cache_enabled: true
    cache_ttl: 3600
    max_conversation_turns: 15
    max_message_length: 1000

# ============================================================================
# 数据库查询默认模板 - 解决高危硬编码问题
# ============================================================================
database:
  queries:
    # 会话相关查询
    conversations:
      get_domain_category: "SELECT domain_id, category_id FROM conversations WHERE conversation_id = ? AND user_id = ?"
      update_domain: "UPDATE conversations SET domain_id = ?, category_id = ?, updated_at = ? WHERE conversation_id = ? AND user_id = ?"
      get_by_user: "SELECT * FROM conversations WHERE user_id = ? ORDER BY created_at DESC"

    # 会话状态相关查询
    session_states:
      insert_or_replace: "INSERT OR REPLACE INTO session_states (session_id, user_id, current_state, updated_at) VALUES (?, ?, ?, ?)"
      get_current_state: "SELECT current_state FROM session_states WHERE session_id = ? AND user_id = ?"
      
    # 文档相关查询
    documents:
      find_by_conversation: "SELECT document_id FROM documents WHERE conversation_id = ? AND user_id = ?"
      get_active_document: "SELECT document_id FROM documents WHERE conversation_id = ? AND user_id = ? AND status = 'active'"
      get_draft_document: "SELECT document_id FROM documents WHERE conversation_id = ? AND user_id = ? AND status = 'draft'"
      update_status: "UPDATE documents SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE document_id = ? AND user_id = ?"
      
    # 消息相关查询
    messages:
      get_first_user_message: "SELECT content FROM messages WHERE conversation_id = ? AND user_id = ? AND sender_type = 'user' ORDER BY created_at ASC LIMIT 1"
      get_recent_messages: "SELECT * FROM messages WHERE conversation_id = ? AND user_id = ? ORDER BY created_at DESC LIMIT ?"
      insert_message: "INSERT INTO messages (conversation_id, user_id, sender_type, content, created_at) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)"
      
    # 关注点相关查询
    focus_points:
      get_definitions: "SELECT focus_id as id, name, description, priority, example, required FROM focus_point_definitions WHERE category_id = ? ORDER BY priority, focus_id"
      get_user_focus_points: "SELECT focus_id, status, attempts, extracted_info, is_covered, updated_at FROM concern_point_coverage WHERE conversation_id = ? AND user_id = ? ORDER BY updated_at"
      update_status: "UPDATE concern_point_coverage SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE focus_id = ? AND conversation_id = ? AND user_id = ?"
      
    # 管理员相关查询
    admin_users:
      create_user: "INSERT INTO admin_users (username, password_hash, salt, role, email) VALUES (?, ?, ?, ?, ?)"
      update_last_login: "UPDATE admin_users SET last_login = CURRENT_TIMESTAMP WHERE id = ?"
      get_user_stats: "SELECT COUNT(DISTINCT user_id) FROM conversations WHERE created_at >= ? AND created_at <= ?"
      authenticate: "SELECT id, username, password_hash, salt, role FROM admin_users WHERE username = ?"
      count_by_role: "SELECT COUNT(*) FROM admin_users WHERE role = ?"

    # 管理员日志相关查询
    admin_logs:
      insert_log: "INSERT INTO admin_logs (admin_id, action, resource, details, ip_address) VALUES (?, ?, ?, ?, ?)"

    # 统计查询
    statistics:
      count_documents_by_user: "SELECT COUNT(*) FROM documents WHERE user_id = ?"
      count_conversations_by_user: "SELECT COUNT(*) FROM conversations WHERE user_id = ?"
      count_messages_by_conversation: "SELECT COUNT(*) FROM messages WHERE conversation_id = ?"
      count_distinct_users: "SELECT COUNT(DISTINCT user_id) FROM conversations"

    # 对话处理相关查询
    conversation_management:
      clear_domain_category: "UPDATE conversations SET domain_id = NULL, category_id = NULL, updated_at = ? WHERE conversation_id = ? AND user_id = ?"
      update_completion_status: "UPDATE conversations SET status = 'completed', updated_at = ? WHERE conversation_id = ? AND user_id = ?"

    # 模板版本相关查询
    template_versions:
      get_by_id_version: "SELECT * FROM template_versions WHERE template_id = ? AND version = ? AND language = ?"
      update_status_deprecated: "UPDATE template_versions SET status = ?, updated_at = ? WHERE template_id = ? AND language = ? AND status = ?"
      update_status_active: "UPDATE template_versions SET status = ?, updated_at = ? WHERE template_id = ? AND version = ? AND language = ?"
      get_latest_version: "SELECT version FROM template_versions WHERE template_id = ? ORDER BY created_at DESC LIMIT 1"

    # 关注点状态相关查询 (使用现有的concern_point_coverage表)
    focus_points_status:
      get_status: "SELECT status FROM concern_point_coverage WHERE conversation_id = ? AND user_id = ? AND focus_id = ?"
      get_all_status: "SELECT focus_id as point_id, status FROM concern_point_coverage WHERE conversation_id = ? AND user_id = ?"
      update_to_pending: "UPDATE concern_point_coverage SET status = 'pending', updated_at = ? WHERE conversation_id = ? AND user_id = ? AND status = 'processing'"
      delete_by_session: "DELETE FROM concern_point_coverage WHERE conversation_id = ? AND user_id = ?"

# ============================================================================
# 消息模板默认配置 - 解决中危硬编码问题
# ============================================================================
message_templates:
  # 错误消息模板
  error:
    general: "抱歉，处理您的请求时发生错误，请稍后重试。"
    unknown_error: "系统遇到未知错误，我们正在处理中。"
    timeout: "请求超时，请检查网络连接后重试。"
    invalid_input: "输入格式不正确，请检查后重新输入。"
    permission_denied: "权限不足，无法执行此操作。"
    processing_failed: "抱歉，处理您的请求时遇到了问题。"
    document_not_found: "抱歉，我没有找到需要修改的文档。"
    document_save_failed: "抱歉，保存修改后的文档时出错，请稍后再试。"
    internal_error: "抱歉，处理您的修改请求时发生了内部错误。"
    system_problem: "抱歉，系统遇到了一些问题，请稍后再试。"
    knowledge_base_error: "抱歉，查询知识库时出现错误。"
    no_knowledge_found: "抱歉，我在知识库中没有找到相关信息。"
    cannot_generate_reply: "抱歉，我无法基于现有信息生成合适的回答。"
    no_information: "抱歉，我没有找到相关信息。"
    generation_failed: "抱歉，暂时无法生成个性化回复。"
    language_reorganize: "抱歉，我需要重新组织一下语言。"
    reply_generation_failed: "抱歉，暂时无法生成回复。"
    reply_error: "抱歉，生成回复时出现错误。"
    request_timeout: "抱歉，处理您的请求超时，请稍后再试。"
    no_valid_response: "抱歉，系统未能生成有效响应，请稍后再试。"
    processing_failure: "抱歉，系统处理失败，请稍后再试。"
    document_modification_failed: "抱歉，文档修改失败。请重新描述您的修改需求。"
    technical_issue: "抱歉，我遇到了一些技术问题。请稍后再试。"
    session_error: "抱歉，无法处理其他会话的消息。"
    message_processing_error: "抱歉，处理消息时遇到了问题。"
    request_processing_error: "很抱歉，我在处理您的请求时遇到了问题。我是AI需求采集助手，可以帮助您整理业务需求。请告诉我您需要什么帮助？"
    temporary_unavailable: "抱歉，我暂时无法处理您的请求。请稍后再试。"
    answer_processing_error: "抱歉，我在处理您的回答时遇到了问题。请重新描述一下您的需求。"
    understanding_issue: "抱歉，我可能没有完全理解您的意思。"
    unclear_understanding: "抱歉我没理解清楚，请再详细说明一下您的项目需求，我会认真倾听。"
    requirement_clarification: "抱歉，我可能没有完全理解您的意思。作为需求采集助手，我主要专长于帮助整理业务需求、分析功能规格和生成需求文档。您能重新描述一下您的需要吗？"
    rephrase_request: "抱歉，请您换个方式描述，我会尽力帮助您。"
    general_problem: "抱歉，我遇到了一些问题。请告诉我您的具体需求，我会尽力帮助您。"
    detailed_clarification_template: |
      抱歉，我可能没有完全理解您的意思。

      您刚才提到："{message_preview}..."

      **🤔 需要澄清的地方：**
      1. 这个项目的主要目标我理解对了吗？
      2. 我是否遗漏了什么重要信息？
      3. 您还有什么补充的要点？

      **💡 建议**
      请您详细描述一下项目的具体需求，包括：
      - 项目类型和规模
      - 主要功能需求
      - 技术要求或偏好
      - 预期时间和预算

      这样我能更准确地为您提供帮助！

  # 问候消息模板
  greeting:
    basic: "您好！我是由己AI助手，可以帮助您完成各种任务。请问有什么可以帮您的吗？"
    general_assistant: "您好！我是由己AI助手，专门为您提供业务领域的专业建议和指导。"
    welcome: "欢迎使用由己AI需求采集系统！"
    requirement_analyst: "您好！我是AI需求分析师，专门帮助您梳理和分析项目需求。请告诉我您想要做什么项目？"
    service_ready: "您好！我是AI需求分析师，很高兴为您服务！请告诉我您的项目需求。"
    happy_to_help: "您好！很高兴为您服务。请问有什么可以帮您？"
    requirement_assistant: "您好！我是AI需求采集助手，可以帮助您整理和分析业务需求。请告诉我您想要咨询什么内容？"

  # 澄清请求模板
  clarification:
    need_more_info: "我理解您的问题，但需要更多信息来为您提供准确的帮助。请告诉我更多关于您具体需求的详细信息。"
    general_request: "请提供更多详细信息，以便我更好地为您服务。"
    unclear_intent: "我没有完全理解您的意思，能否请您重新描述一下？"
    not_understand: "抱歉，我没有完全理解您的意思。能否请您再详细说明一下？"
    exception_occurred: "抱歉，刚才的处理出现了异常。请您重新描述一下需求。"
    problem_encountered: "抱歉，我遇到了一些问题。请您重新描述一下需求，我会尽力帮助您。"
    question_unclear: "抱歉我的问题可能不够清晰。让我换一种方式问：请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。"
    document_dissatisfaction: "非常抱歉文档未能让您满意。为了能更正错误，您能具体指出需要修改的部分吗？"
    cannot_generate_suitable: "抱歉，我暂时无法生成合适的回复。"

  # 系统状态消息
  system:
    processing: "正在处理您的请求，请稍候..."
    completed: "处理完成！"
    fallback: "系统正在使用备用处理方式。"

  # 关键词加速器响应模板
  keyword_accelerator:
    greeting: "您好！我是您的AI助手，很高兴为您服务。请告诉我您的需求，我会帮助您进行需求分析和文档生成。"
    confirm: "好的，我已确认您的选择。"
    restart: "好的，让我们重新开始。请告诉我您的新需求。"
    system_capability_query: |
      我是一个智能需求采集助手，可以帮助您：
      1. 分析和整理项目需求
      2. 生成专业的需求文档
      3. 提供项目建议和指导

      请告诉我您的具体需求，我会为您提供专业的帮助。
    modify: "我理解您需要修改，请告诉我具体需要调整的内容。"
    default_response: "我理解您的{intent}请求，让我为您处理。"

  # 对话状态机响应模板
  state_machine:
    greeting_fallback: "您好！"
    greeting_default: "您好！我是您的AI需求分析师。"
    new_project_greeting: "您好！有什么新的项目需要我帮您分析吗？"

  # 对话处理器响应模板
  conversation_handler:
    welcome_default: "您好！我是AI需求分析师，专门帮助您梳理和分析项目需求。请告诉我您想要做什么项目？"

  # 消息回复管理器响应模板
  message_reply_manager:
    unknown_action_fallback: "抱歉，我暂时无法处理您的请求。"

  # 对话流程回复混入响应模板
  conversation_flow_reply_mixin:
    general_problem: "抱歉，我遇到了一些问题。请稍后再试。"

  # 基础Agent响应模板
  base_agent:
    processing_with_error: "正在处理您的请求。\n抱歉，{error_msg}"

  # 能力策略响应模板
  capabilities_strategy:
    closing_guidance: "\n有什么具体需求或问题，请随时告诉我！"
    # 🔧 [硬编码消除] 能力介绍模板
    capability_introduction: |
      我是AI需求采集助手，专门帮助您整理和分析业务需求。我的主要能力包括：

      🎯 **需求收集**：帮助您梳理和整理业务需求，确保需求的完整性和准确性

      📋 **需求分析**：对收集到的需求进行分类、优先级排序和可行性分析

      📝 **文档生成**：自动生成规范的需求文档，包括功能规格说明等

      💡 **智能建议**：基于行业经验提供优化建议和最佳实践

      🔄 **交互式对话**：通过自然语言对话的方式，让需求收集过程更加高效和友好

      有什么具体问题我可以帮助您解答吗？

    # 🔧 [硬编码消除] 帮助指导模板
    help_guidance: |
      我来帮助您！作为AI需求采集助手，我可以协助您：

      📝 **开始需求收集**：告诉我您的项目背景，我会引导您逐步梳理需求
      🔍 **分析现有需求**：如果您已有部分需求，我可以帮您完善和分析
      📊 **需求优先级排序**：帮助您确定需求的重要性和紧急程度
      📋 **生成需求文档**：整理讨论结果，生成标准的需求文档
      ❓ **解答疑问**：关于需求分析的任何问题，我都很乐意帮助

      请告诉我您想从哪里开始？

  # 情感支持策略响应模板
  emotional_support_strategy:
    anger_response_1: "我理解您现在的愤怒情绪。深呼吸一下，让我们一起来看看如何解决这个问题。"
    anxiety_response_1: "我理解您的担心和焦虑。焦虑是很正常的情绪反应，让我们一起分析一下具体的担忧。"
    confused_response_1: "我理解您的困惑。让我们一步步来解释，直到您完全明白为止。"
    fallback_response: "我理解您的感受。无论遇到什么困难，我都会尽力帮助您。请告诉我具体的情况。"
    default_understanding: "我理解您的感受，让我来帮助您。"

  # 🔧 [硬编码消除] 需求采集模板
  requirement_gathering:
    # 需求采集开始模板
    start_template: |
      感谢您提供的信息！我来帮助您整理这个需求。

      基于您描述的内容："{message_preview}..."

      让我们从以下几个方面来详细梳理您的需求：

      **📋 基本信息确认**
      1. 项目/业务的核心目标是什么？
      2. 预期要解决的主要问题是什么？
      3. 目标用户群体的特征？

      **💰 资源和约束**
      1. 预算范围大概是多少？
      2. 时间期望（什么时候需要完成）？
      3. 团队资源情况？

      **🎯 期望成果**
      1. 您希望达到什么具体效果？
      2. 如何衡量项目是否成功？

      请从您最关心的方面开始回答，我会根据您的回答进一步深入了解。

    # 澄清问题模板 - 第二次尝试
    clarification_attempt_2: |
      我理解您之前提到"{original_message}"，刚才您回答了"{clarification_answer}"。

      为了确保为您提供最准确的服务，让我更直接地询问：

      **请从以下选项中选择最接近的一项：**

      1️⃣ **设计创意** - 比如：公司Logo、产品海报、品牌形象、UI界面设计
      2️⃣ **技术开发** - 比如：企业网站、手机APP、小程序、管理系统
      3️⃣ **营销推广** - 比如：活动策划、广告投放、社交媒体运营、品牌推广
      4️⃣ **法律服务** - 比如：合同起草、知识产权申请、法律风险评估

      **请直接回复数字（如"1"）或说出具体类型（如"设计创意"）**

    # 澄清问题模板 - 第三次及以上尝试
    clarification_attempt_3_plus: |
      让我们简化一下，请直接告诉我：

      🎨 **设计** - Logo、海报、界面等视觉设计
      💻 **开发** - 网站、APP、软件等技术开发
      📢 **营销** - 推广、广告、活动等营销服务
      ⚖️ **法律** - 合同、知识产权等法律服务

      **请直接说出您需要的服务类型**

    # 回退问题模板
    fallback_question: "关于「{focus_point_name}」，{description}"
    fallback_question_with_example: |
      关于「{focus_point_name}」，{description}

      💡 **参考示例**：{example}

    # 🔧 [硬编码消除] 跳过问题模板
    skip_question_template: |
      没关系，我们可以先跳过这个问题。

      让我们从另一个角度来了解您的需求：

      **📊 项目背景**
      1. 这个项目是为了解决什么具体问题？
      2. 目前的现状是什么样的？
      3. 您希望通过这个项目达到什么目标？

      **👥 相关人员**
      - 项目涉及哪些人员或团队？
      - 主要的决策者是谁？

      请从您觉得最重要的方面开始描述。

    # 🔧 [硬编码消除] 回退问题模板（用于智能问题生成失败时）
    fallback_question_simple: "关于「{focus_point_name}」，{description}"
    fallback_question_with_example: |
      关于「{focus_point_name}」，{description}

      💡 **参考示例**：{example}

  # 知识库处理器响应模板
  knowledge_base_handler:
    system_unavailable: "系统暂时无法处理您的请求，请稍后再试。"

  # 文档处理器响应模板
  document_handler:
    confirmation_success: "✅ 太好了！需求文档已确认完成。\n\n感谢您的配合，如果后续有新的需求，随时可以联系我！"

  # 复合处理器响应模板
  composite_handler:
    continue_collecting: "继续收集您的需求信息..."

  # 知识库策略响应模板
  knowledge_base_strategy:
    search_template: "我来为您查找相关信息。{search_prompt}"
    
  # 业务介绍模板
  introduction:
    youji_platform: |
      您好！我是由己AI助手，专门为您提供业务领域的专业建议和指导。
      
      🏢 **由己平台核心业务**：
      • 智能需求采集系统 - 通过AI对话收集和分析项目需求
      • 在线用工平台 - 连接企业与全球优秀人才
      • 项目管理工具 - 提供全流程项目管理解决方案
      
      我可以帮助您：
      📋 收集和整理项目需求
      💡 提供业务咨询和建议
      🔍 分析需求的完整性和可行性
      📊 生成专业的需求文档
      
      请告诉我您的具体需求，我将为您提供专业的帮助！

# ============================================================================
# 阈值和性能参数默认配置
# ============================================================================
thresholds:
  # 业务逻辑阈值
  completion_threshold: 0.7
  confidence_threshold: 0.8
  similarity_threshold: 0.9
  requirement_completeness: 0.75
  keyword_match_threshold: 0.9

  # 性能相关阈值
  response_time_limit: 5.0
  memory_usage_limit: 0.8
  cache_hit_ratio: 0.6

  # 重试和超时配置
  max_retry_attempts: 3
  default_timeout: 5
  long_timeout: 30
  short_timeout: 2

  # 数量限制
  max_focus_points: 10
  max_conversation_turns: 15
  max_message_length: 1000
  max_document_size: 5242880  # 5MB

  # 策略相关阈值
  strategy:
    requirement:
      keyword_match_multiplier: 3
      max_keyword_score: 0.9

# ============================================================================
# LLM默认配置
# ============================================================================
llm:
  default_model: "deepseek-chat"
  
  # 默认LLM参数
  default_params:
    temperature: 0.7
    max_tokens: 2000
    top_p: 0.9
    frequency_penalty: 0.0
    presence_penalty: 0.0
    
  # 场景特定参数
  scenario_params:
    intent_recognition:
      temperature: 0.3
      max_tokens: 500
    document_generation:
      temperature: 0.5
      max_tokens: 4000
    domain_classification:
      temperature: 0.2
      max_tokens: 200

# ============================================================================
# 安全和验证默认配置
# ============================================================================
security:
  input_validation:
    max_input_length: 10000
    allowed_file_types: ["txt", "md", "pdf", "doc", "docx"]
    max_file_size: 10485760  # 10MB
    
  rate_limiting:
    requests_per_minute: 60
    requests_per_hour: 1000
    
  session:
    timeout: 3600  # 1小时
    max_sessions_per_user: 5

# ============================================================================
# 知识库默认配置
# ============================================================================
knowledge_base:
  enabled: false
  
  chroma_db:
    path: "backend/data/chroma_db"
    collection_name: "hybrid_knowledge_base"
    
  retrieval:
    top_k: 3
    similarity_threshold: 0.3
    max_context_length: 2000
    
  performance:
    cache_enabled: true
    cache_ttl: 3600
    max_concurrent_queries: 5
