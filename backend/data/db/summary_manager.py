"""
管理 conversation_summaries 表的 CRUD 操作。
这个类负责处理每个对话的最新状态摘要。

设计说明：
- 使用异步操作提高并发性能
- 内置JSON格式验证确保数据完整性
- 完善的错误处理和日志记录
- 遵循单一职责原则，只处理摘要相关操作

依赖关系：
- 依赖于 DatabaseManager 进行底层数据库操作
- 从 config_manager 获取SQL查询模板
"""

import json
from .database_manager import DatabaseManager
import logging
from backend.config.unified_config_loader import get_unified_config

class SummaryManager:
    """
    管理 conversation_summaries 表的 CRUD 操作。
    这个类负责处理每个对话的最新状态摘要。
    """
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化摘要管理器。

        Args:
            db_manager: 数据库管理器实例。concern_point_coverage
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)

    async def get_summary(self, conversation_id: str, user_id: str) -> str:
        """
        根据 conversation_id 和 user_id 获取最新的对话摘要。
        方法逻辑：
        1. 从配置中获取预定义的SQL查询
        2. 执行异步数据库查询
        3. 处理三种可能的结果：
       - 找到有效摘要：返回JSON字符串
       - 未找到记录：返回空JSON对象
       - 发生异常：记录错误并返回空JSON对象

        性能考虑：
        - 查询使用 conversation_id 和 user_id 作为主键索引，确保高效查找
        - 轻量级返回，避免不必要的数据处理

        安全考虑：
        - 任何错误情况都返回安全的空JSON，避免上层应用崩溃
        """

        query = get_unified_config().get_database_query("summaries.get_summary")
        try:
            result = await self.db_manager.get_record(query, (conversation_id, user_id))
            if result and result.get('summary_json'):
                self.logger.debug(f"成功获取到 conversation_id '{conversation_id}' (user: {user_id}) 的摘要。")
                return result['summary_json']
            else:
                self.logger.debug(f"未找到 conversation_id '{conversation_id}' (user: {user_id}) 的摘要，返回空对象。")
                return "{}"
        except Exception as e:
            self.logger.error(f"获取摘要失败 (conversation_id: {conversation_id}, user_id: {user_id}): {e}", exc_info=True)
            return "{}" # 出错时也返回安全的空JSON

    async def update_summary(self, conversation_id: str, user_id: str, new_summary_json: str):
        """
        使用 UPSERT 逻辑，更新或插入一个对话的摘要。

        方法流程：
        1. 首先验证输入JSON的合法性
        2. 执行UPSERT操作(存在则更新，不存在则插入)
        3. 记录操作结果

        数据验证：
        - 使用json.loads严格验证输入JSON格式
        - 无效JSON会立即拒绝并记录错误

        事务保证：
        - 依赖DatabaseManager的事务处理能力
        - 单条记录操作，保持原子性

        性能优化：
        - 使用预编译SQL语句
        - UPSERT操作减少先查询后更新的开销

        Args:
            conversation_id: 对话的唯一标识符。
            user_id: 用户的唯一标识符。
            new_summary_json: 最新的、完整的JSON摘要字符串。
        """
        query = get_unified_config().get_database_query("summaries.upsert_summary")
        try:
            # 验证传入的是否为合法的JSON字符串
            json.loads(new_summary_json)

            await self.db_manager.execute_update(query, (conversation_id, user_id, new_summary_json))
            self.logger.info(f"成功更新了 conversation_id '{conversation_id}' (user: {user_id}) 的摘要。")
        except json.JSONDecodeError as e:
            self.logger.error(f"更新摘要失败：传入的字符串不是有效的JSON (conversation_id: {conversation_id}, user_id: {user_id}): {e}")
        except Exception as e:
            self.logger.error(f"更新摘要失败 (conversation_id: {conversation_id}, user_id: {user_id}): {e}", exc_info=True)
