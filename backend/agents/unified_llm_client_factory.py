#!/usr/bin/env python3
"""
统一LLM客户端工厂
统一所有LLM客户端的创建方式，解决多种创建方式并存的问题
"""

import logging
from typing import Dict, Any
from backend.config.unified_config_loader import get_unified_config


class UnifiedLLMClientFactory:
    """统一的LLM客户端工厂"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._client_cache = {}

        # 默认配置
        self.default_config = {
            "provider": "openai",
            "model": "gpt-3.5-turbo",
            "temperature": 0.7,
            "max_tokens": 1000,
            "timeout": 30
        }

    def create_client(self, config_type: str = "default", **kwargs) -> Any:
        """
        根据配置类型创建LLM客户端

        Args:
            config_type: 配置类型 (default, domain_classification, question_optimization等)
            **kwargs: 额外的配置参数，会覆盖默认配置

        Returns:
            LLM客户端实例
        """
        # 生成缓存键
        cache_key = f"{config_type}_{hash(frozenset(kwargs.items()))}"

        # 检查缓存
        if cache_key in self._client_cache:
            return self._client_cache[cache_key]

        # 获取配置
        config = self._get_config(config_type, **kwargs)

        # 创建客户端
        client = self._create_llm_client(config)

        # 缓存客户端
        self._client_cache[cache_key] = client

        self.logger.info(f"创建LLM客户端: {config_type}, 模型: {config.get('model')}, 温度: {config.get('temperature')}")

        return client

    def _get_config(self, config_type: str, **kwargs) -> Dict[str, Any]:
        """获取指定类型的配置"""
        # 从统一配置管理器获取基础配置
        base_config = self.default_config.copy()

        # 尝试从配置文件获取特定配置
        try:
            # 从统一配置获取LLM配置
            config = get_unified_config()
            llm_config = config.get_config_value('llm')
            if llm_config:

                # 获取默认配置
                if 'default' in llm_config:
                    base_config.update(llm_config['default'])

                # 获取特定类型配置
                if config_type in llm_config:
                    base_config.update(llm_config[config_type])
        except Exception as e:
            self.logger.warning(f"获取配置失败，使用默认配置: {e}")

        # 应用特定配置类型的默认值
        type_specific_config = self._get_type_specific_config(config_type)
        base_config.update(type_specific_config)

        # 应用用户提供的参数
        base_config.update(kwargs)

        return base_config

    def _get_type_specific_config(self, config_type: str) -> Dict[str, Any]:
        """获取特定类型的默认配置"""
        type_configs = {
            "domain_classification": {
                "temperature": 0.2,  # 分类任务需要更稳定的结果
                "max_tokens": 100
            },
            "category_classification": {
                "temperature": 0.2,
                "max_tokens": 100
            },
            "intent_recognition": {
                "temperature": 0.3,
                "max_tokens": 200
            },
            "information_extraction": {
                "temperature": 0.4,
                "max_tokens": 800
            },
            "question_optimization": {
                "temperature": 0.8,  # 问题生成需要更多创意
                "max_tokens": 500
            },
            "document_generation": {
                "model": "gpt-4",  # 使用更强的模型生成文档
                "temperature": 0.5,
                "max_tokens": 2000
            },
            "conversation": {
                "temperature": 0.7,
                "max_tokens": 1000
            }
        }

        return type_configs.get(config_type, {})

    def _create_llm_client(self, config: Dict[str, Any]) -> Any:
        """创建实际的LLM客户端"""
        provider = config.get("provider", "openai")

        if provider == "openai":
            return self._create_openai_client(config)
        else:
            raise ValueError(f"不支持的LLM提供商: {provider}")

    def _create_openai_client(self, config: Dict[str, Any]) -> Any:
        """创建OpenAI客户端"""
        try:
            # 尝试导入现有的LLM服务
            from backend.agents.llm_service import LLMService

            # 创建LLM服务实例
            llm_service = LLMService()

            # 设置配置参数
            if hasattr(llm_service, 'set_config'):
                llm_service.set_config(config)

            return llm_service

        except ImportError:
            self.logger.warning("无法导入LLMService，尝试创建基础OpenAI客户端")

            try:
                import openai

                # 创建基础OpenAI客户端
                client = openai.OpenAI()

                # 包装配置
                class OpenAIWrapper:
                    def __init__(self, client, config):
                        self.client = client
                        self.config = config

                    def chat_completion(self, messages, **kwargs):
                        # 合并配置
                        params = {
                            "model": self.config.get("model", "gpt-3.5-turbo"),
                            "temperature": self.config.get("temperature", 0.7),
                            "max_tokens": self.config.get("max_tokens", 1000),
                            "messages": messages
                        }
                        params.update(kwargs)

                        return self.client.chat.completions.create(**params)

                return OpenAIWrapper(client, config)

            except ImportError:
                raise ImportError("无法导入OpenAI库，请安装: pip install openai")

    def get_available_config_types(self) -> list:
        """获取可用的配置类型"""
        return [
            "default",
            "domain_classification",
            "category_classification",
            "intent_recognition",
            "information_extraction",
            "question_optimization",
            "document_generation",
            "conversation"
        ]

    def clear_cache(self):
        """清空客户端缓存"""
        self._client_cache.clear()
        self.logger.info("LLM客户端缓存已清空")

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            "cached_clients": len(self._client_cache),
            "cache_keys": list(self._client_cache.keys())
        }


# 创建全局工厂实例
unified_llm_factory = UnifiedLLMClientFactory()


def create_llm_client(config_type: str = "default", **kwargs) -> Any:
    """
    便捷函数：创建LLM客户端

    Args:
        config_type: 配置类型
        **kwargs: 额外配置参数

    Returns:
        LLM客户端实例
    """
    return unified_llm_factory.create_client(config_type, **kwargs)


def get_available_llm_types() -> list:
    """获取可用的LLM配置类型"""
    return unified_llm_factory.get_available_config_types()


# 向后兼容的别名
LLMClientFactory = UnifiedLLMClientFactory
llm_factory = unified_llm_factory
