#!/usr/bin/env python3
"""
配置兼容性层

为了保持向后兼容性，提供统一的配置访问接口。
在新旧配置系统之间提供透明的切换能力。

主要功能：
1. 保持现有API不变
2. 支持新旧配置系统切换
3. 提供配置状态监控
4. 处理配置迁移过程中的兼容性问题
"""

import logging
from typing import Dict, Any, Optional
from .unified_config_loader import get_unified_config as get_legacy_config
from .modular_loader import get_modular_config_loader
from .compatibility_fixes import convert_dict_to_llm_config, normalize_message_template_path, extract_nested_config

logger = logging.getLogger(__name__)


class CompatibilityConfigWrapper:
    """配置兼容性包装器"""

    def __init__(self):
        self.legacy_config = get_legacy_config()
        self.modular_loader = get_modular_config_loader()
        self.logger = logging.getLogger(__name__)

    def is_modular_enabled(self) -> bool:
        """检查是否启用模块化配置"""
        return self.modular_loader.is_enabled()

    def get_config_status(self) -> Dict[str, Any]:
        """获取配置状态"""
        status = {
            "legacy_config_available": True,
            "modular_config_enabled": self.is_modular_enabled(),
            "current_mode": "modular" if self.is_modular_enabled() else "legacy"
        }

        if self.is_modular_enabled():
            status.update(self.modular_loader.get_monitoring_stats())

        return status

    def get_config_value(self, key: str, default: Any = None) -> Any:
        """获取配置值（兼容原有接口）"""
        if self.is_modular_enabled():
            # 使用模块化配置
            result = self.modular_loader.get_config(key, default)
            if result is not None:
                return result

        # 回退到传统配置
        return self.legacy_config.get_config_value(key, default)

    def get_llm_config(self, config_type: str = 'default') -> Dict[str, Any]:
        """获取LLM配置（兼容原有接口）"""
        if self.is_modular_enabled():
            # 尝试从模块化配置获取
            models_config = self.modular_loader.get_config("llm.models", {})
            scenarios_config = self.modular_loader.get_config("llm.scenarios", {})

            if models_config or scenarios_config:
                return self._merge_llm_configs(models_config, scenarios_config, config_type)

        # 回退到传统配置
        return self.legacy_config.get_llm_config(config_type)

    def get_business_rules(self) -> Dict[str, Any]:
        """获取业务规则（兼容原有接口）"""
        if self.is_modular_enabled():
            result = self.modular_loader.get_config("business.rules", {})
            if result:
                return result

        # 回退到传统配置
        return self.legacy_config.get_business_rules()

    def get_message_templates(self) -> Dict[str, Any]:
        """获取消息模板（兼容原有接口）"""
        if self.is_modular_enabled():
            result = self.modular_loader.get_config("business.templates", {})
            if result:
                return result

        # 回退到传统配置
        return self.legacy_config.get_message_templates()

    def get_message_template(self, template_path: str, default: str = "", **kwargs) -> str:
        """获取消息模板（兼容原有接口）"""
        if self.is_modular_enabled():
            templates = self.modular_loader.get_config("business.templates", {})
            if templates:
                template = self._get_nested_value(templates, template_path.split('.'), default)
                if template and kwargs:
                    try:
                        return template.format(**kwargs)
                    except (KeyError, ValueError):
                        return template
                return template

        # 回退到传统配置
        return self.legacy_config.get_message_template(template_path, default, **kwargs)

    def get_threshold(self, threshold_path: str, default: Any = None) -> Any:
        """获取阈值配置（兼容原有接口）"""
        if self.is_modular_enabled():
            thresholds = self.modular_loader.get_config("business.thresholds", {})
            if thresholds:
                return self._get_nested_value(thresholds, threshold_path.split('.'), default)

        # 回退到传统配置
        return self.legacy_config.get_threshold(threshold_path, default)

    def _merge_llm_configs(self, models_config: Dict, scenarios_config: Dict, config_type: str) -> Dict[str, Any]:
        """合并LLM配置"""
        # 获取默认配置
        default_config = models_config.get('default_params', {})

        # 获取特定场景配置
        scenario_params = scenarios_config.get('scenario_params', {})
        if config_type in scenario_params:
            scenario_config = scenario_params[config_type]
            return {**default_config, **scenario_config}

        return default_config

    def _get_nested_value(self, data: Dict[str, Any], path: list, default: Any = None) -> Any:
        """获取嵌套字典中的值"""
        current = data
        for key in path:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default
        return current


# 为了向后兼容，保留原有的CompatibilityConfigManager
class CompatibilityConfigManager:
    """兼容性配置管理器（保持向后兼容）"""

    def __init__(self):
        self._wrapper = CompatibilityConfigWrapper()

    def get_strategies(self):
        """获取策略配置（兼容性方法）"""
        return self._wrapper.legacy_config.get_conversation_config()

    def get_business_rule(self, key, default=None):
        """获取业务规则（兼容性方法）"""
        return self._wrapper.get_config_value(f'business_rules.{key}', default)

    def get_message_template(self, key, **kwargs):
        """获取消息模板（兼容性方法）"""
        return self._wrapper.get_message_template(key, **kwargs)

    def get_message_config(self, section):
        """获取消息配置（兼容性方法）"""
        return self._wrapper.get_config_value(f'message_templates.{section}')

    def reload_all(self):
        """重新加载所有配置（兼容性方法）"""
        if self._wrapper.is_modular_enabled():
            self._wrapper.modular_loader.reload_config()
        else:
            self._wrapper.legacy_config.reload()

    def get_config_status(self):
        """获取配置状态（兼容性方法）"""
        return self._wrapper.get_config_status()


# 全局实例
compatibility_wrapper = CompatibilityConfigWrapper()
unified_config_manager = CompatibilityConfigManager()


def get_compatibility_wrapper() -> CompatibilityConfigWrapper:
    """获取兼容性包装器实例"""
    return compatibility_wrapper
