# -*- coding: utf-8 -*-
"""
会话管理器

提供安全的会话管理功能，包括：
- 获取活跃会话（按用户隔离）
- 清理过期会话（按用户隔离）
- 导出会话数据（安全版本）
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from .database_manager import DatabaseManager
from backend.config.unified_config_loader import get_unified_config


class ConversationManager:
    """会话管理器 - 提供安全的会话管理功能"""

    def __init__(self, db_manager: DatabaseManager):
        """
        初始化会话管理器

        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)

    async def get_active_conversations(self, user_id: str, since_hours: int = 24) -> List[Dict[str, Any]]:
        """
        获取用户的活跃会话

        Args:
            user_id: 用户ID
            since_hours: 活跃时间阈值（小时）

        Returns:
            List[Dict[str, Any]]: 活跃会话列表
        """
        try:
            # 计算时间阈值
            threshold_time = datetime.now() - timedelta(hours=since_hours)
            threshold_str = threshold_time.isoformat()

            # 执行查询 - 注意参数顺序：threshold_time, user_id
            results = await self.db_manager.execute_query(
                get_unified_config().get_database_query("conversations.get_active"),
                (threshold_str, user_id)
            )

            self.logger.info(f"用户 {user_id} 有 {len(results)} 个活跃会话")
            return results

        except Exception as e:
            self.logger.error(f"获取活跃会话失败 (user_id: {user_id}): {e}")
            return []

    async def get_expired_conversations(self, user_id: str, max_age_hours: int = 168) -> List[Dict[str, Any]]:
        """
        获取用户的过期会话

        Args:
            user_id: 用户ID
            max_age_hours: 最大存活时间（小时，默认7天）

        Returns:
            List[Dict[str, Any]]: 过期会话列表
        """
        try:
            # 计算过期时间阈值
            expiry_time = datetime.now() - timedelta(hours=max_age_hours)
            expiry_str = expiry_time.isoformat()

            # 执行查询 - 注意参数顺序：expiry_time, user_id
            results = await self.db_manager.execute_query(
                get_unified_config().get_database_query("conversations.get_expired"),
                (expiry_str, user_id)
            )

            self.logger.info(f"用户 {user_id} 有 {len(results)} 个过期会话")
            return results

        except Exception as e:
            self.logger.error(f"获取过期会话失败 (user_id: {user_id}): {e}")
            return []

    async def delete_expired_conversations(self, user_id: str, max_age_hours: int = 168) -> int:
        """
        删除用户的过期会话

        Args:
            user_id: 用户ID
            max_age_hours: 最大存活时间（小时，默认7天）

        Returns:
            int: 删除的会话数量
        """
        try:
            # 先获取要删除的会话列表（用于日志记录）
            expired_conversations = await self.get_expired_conversations(user_id, max_age_hours)

            if not expired_conversations:
                self.logger.info(f"用户 {user_id} 没有过期会话需要删除")
                return 0

            # 计算过期时间阈值
            expiry_time = datetime.now() - timedelta(hours=max_age_hours)
            expiry_str = expiry_time.isoformat()

            # 执行删除 - 注意参数顺序：expiry_time, user_id
            await self.db_manager.execute_update(
                get_unified_config().get_database_query("conversations.delete_expired"),
                (expiry_str, user_id)
            )

            deleted_count = len(expired_conversations)
            self.logger.info(f"已删除用户 {user_id} 的 {deleted_count} 个过期会话")

            return deleted_count

        except Exception as e:
            self.logger.error(f"删除过期会话失败 (user_id: {user_id}): {e}")
            return 0

    async def export_conversation_data(self, conversation_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        安全地导出会话数据

        Args:
            conversation_id: 会话ID
            user_id: 用户ID（必须匹配，防止数据泄露）

        Returns:
            Optional[Dict[str, Any]]: 会话数据，如果没有权限则返回None
        """
        try:
            # 执行安全查询 - 必须同时匹配conversation_id和user_id
            results = await self.db_manager.execute_query(
                get_unified_config().get_database_query("backup.export_conversation"),
                (conversation_id, user_id)
            )

            if not results:
                self.logger.warning(f"会话 {conversation_id} 不存在或用户 {user_id} 无权访问")
                return None

            conversation_data = results[0]
            self.logger.info(f"成功导出会话 {conversation_id} 的数据 (user: {user_id})")

            return conversation_data

        except Exception as e:
            self.logger.error(f"导出会话数据失败 (conversation_id: {conversation_id}, user_id: {user_id}): {e}")
            return None

    async def update_conversation_activity(self, conversation_id: str, user_id: str) -> bool:
        """
        更新会话的最后活动时间

        Args:
            conversation_id: 会话ID
            user_id: 用户ID

        Returns:
            bool: 更新是否成功
        """
        try:
            now = datetime.now().isoformat()

            await self.db_manager.execute_update(
                get_unified_config().get_database_query("conversations.update_last_activity"),
                (now, now, conversation_id, user_id)
            )

            self.logger.debug(f"已更新会话 {conversation_id} (user: {user_id}) 的活动时间")
            return True

        except Exception as e:
            self.logger.error(f"更新会话活动时间失败: {e}")
            return False

    async def get_conversation_info(self, conversation_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        获取会话信息

        Args:
            conversation_id: 会话ID
            user_id: 用户ID

        Returns:
            Optional[Dict[str, Any]]: 会话信息，如果没有权限则返回None
        """
        try:
            results = await self.db_manager.execute_query(
                get_unified_config().get_database_query("conversations.get_info"),
                (conversation_id, user_id)
            )

            if results:
                return results[0]
            else:
                self.logger.warning(f"会话 {conversation_id} 不存在或用户 {user_id} 无权访问")
                return None

        except Exception as e:
            self.logger.error(f"获取会话信息失败: {e}")
            return None
