"""
安全回退机制

提供知识库功能的错误监控、自动降级和临时禁用功能，
确保系统在出现问题时能够安全回退到原有的需求采集流程。
"""

import logging
import threading
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json
from pathlib import Path

from backend.config.knowledge_base_config import get_knowledge_base_config_manager
from backend.config.unified_config_loader import get_unified_config


class SafetyLevel(Enum):
    """安全级别"""
    NORMAL = "normal"           # 正常运行
    WARNING = "warning"         # 警告状态
    DEGRADED = "degraded"       # 降级运行
    EMERGENCY = "emergency"     # 紧急状态，完全禁用


@dataclass
class ErrorRecord:
    """错误记录"""
    timestamp: datetime
    error_type: str
    error_message: str
    component: str
    severity: str
    context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SafetyMetrics:
    """安全指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    error_rate: float = 0.0
    avg_response_time: float = 0.0
    last_error_time: Optional[datetime] = None
    consecutive_errors: int = 0
    safety_level: SafetyLevel = SafetyLevel.NORMAL


class SafetyManager:
    """
    安全管理器

    功能：
    - 错误监控和统计
    - 自动降级决策
    - 知识库功能临时禁用
    - 系统健康检查和恢复
    - 安全事件记录和报告
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化安全管理器

        Args:
            config_path: 配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        self.kb_config_manager = get_knowledge_base_config_manager()

        # 缓存统一配置对象
        self.unified_config = get_unified_config()

        # 安全配置
        self.config = self._load_safety_config(config_path)

        # 安全指标
        self.metrics = SafetyMetrics()

        # 错误记录
        self.error_history: List[ErrorRecord] = []
        self.max_error_history = 1000

        # 线程锁
        self._lock = threading.RLock()

        # 回调函数
        self.degradation_callbacks: List[Callable] = []
        self.recovery_callbacks: List[Callable] = []

        # 自动恢复定时器
        self._recovery_timer = None

        self.logger.info("安全管理器初始化完成")

    def _load_safety_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """加载安全配置"""
        default_config = {
            "error_thresholds": {
                "max_error_rate": self.unified_config.get_threshold("quality.abuse_detection_threshold", 0.1),
                "max_consecutive_errors": self.unified_config.get_threshold("security.max_login_attempts", 5),
                "warning_error_rate": self.unified_config.get_threshold("quality.similarity_threshold", 0.05),
                "critical_error_rate": self.unified_config.get_threshold("security.content_filter_threshold", 0.2)
            },
            "time_windows": {
                "error_rate_window": self.unified_config.get_threshold("performance.timeout.long", 300),
                "recovery_check_interval": self.unified_config.get_threshold("performance.timeout.long", 60),
                "degradation_cooldown": self.unified_config.get_threshold("performance.timeout.long", 300)
            },
            "auto_recovery": {
                "enabled": True,
                "max_attempts": self.unified_config.get_threshold("performance.retry.default", 3),
                "backoff_multiplier": 2
            },
            "notifications": {
                "enabled": True,
                "log_level": "WARNING"
            }
        }

        if config_path and Path(config_path).exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                self.logger.warning(f"加载安全配置失败，使用默认配置: {e}")

        return default_config

    def record_request(self, success: bool, response_time: float,
                      component: str = "knowledge_base", context: Dict[str, Any] = None):
        """
        记录请求结果

        Args:
            success: 请求是否成功
            response_time: 响应时间
            component: 组件名称
            context: 上下文信息
        """
        with self._lock:
            self.metrics.total_requests += 1

            if success:
                self.metrics.successful_requests += 1
                self.metrics.consecutive_errors = 0
            else:
                self.metrics.failed_requests += 1
                self.metrics.consecutive_errors += 1
                self.metrics.last_error_time = datetime.now()

            # 更新错误率
            if self.metrics.total_requests > 0:
                self.metrics.error_rate = self.metrics.failed_requests / self.metrics.total_requests

            # 更新平均响应时间
            if self.metrics.total_requests == 1:
                self.metrics.avg_response_time = response_time
            else:
                # 使用指数移动平均
                alpha = 0.1
                self.metrics.avg_response_time = (
                    alpha * response_time +
                    (1 - alpha) * self.metrics.avg_response_time
                )

            # 检查是否需要降级
            self._check_degradation_conditions()

    def record_error(self, error_type: str, error_message: str,
                    component: str = "knowledge_base", severity: str = "ERROR",
                    context: Dict[str, Any] = None):
        """
        记录错误

        Args:
            error_type: 错误类型
            error_message: 错误消息
            component: 组件名称
            severity: 严重程度
            context: 上下文信息
        """
        error_record = ErrorRecord(
            timestamp=datetime.now(),
            error_type=error_type,
            error_message=error_message,
            component=component,
            severity=severity,
            context=context or {}
        )

        with self._lock:
            self.error_history.append(error_record)

            # 限制错误历史长度
            if len(self.error_history) > self.max_error_history:
                self.error_history = self.error_history[-self.max_error_history:]

        # 记录请求失败
        self.record_request(success=False, response_time=0, component=component, context=context)

        self.logger.error(f"安全管理器记录错误: {component}.{error_type} - {error_message}")

    def _check_degradation_conditions(self):
        """检查降级条件"""
        thresholds = self.config["error_thresholds"]

        current_level = self.metrics.safety_level
        new_level = current_level

        # 检查连续错误数
        if self.metrics.consecutive_errors >= thresholds["max_consecutive_errors"]:
            new_level = SafetyLevel.EMERGENCY
        # 检查严重错误率
        elif self.metrics.error_rate >= thresholds["critical_error_rate"]:
            new_level = SafetyLevel.EMERGENCY
        # 检查一般错误率
        elif self.metrics.error_rate >= thresholds["max_error_rate"]:
            new_level = SafetyLevel.DEGRADED
        # 检查警告错误率
        elif self.metrics.error_rate >= thresholds["warning_error_rate"]:
            new_level = SafetyLevel.WARNING
        else:
            # 检查是否可以恢复
            if current_level != SafetyLevel.NORMAL and self._can_recover():
                new_level = SafetyLevel.NORMAL

        if new_level != current_level:
            self._change_safety_level(new_level)

    def _change_safety_level(self, new_level: SafetyLevel):
        """改变安全级别"""
        old_level = self.metrics.safety_level
        self.metrics.safety_level = new_level

        self.logger.warning(f"安全级别变更: {old_level.value} -> {new_level.value}")

        # 执行相应的安全措施
        if new_level in [SafetyLevel.DEGRADED, SafetyLevel.EMERGENCY]:
            self._trigger_degradation(new_level)
        elif new_level == SafetyLevel.NORMAL and old_level != SafetyLevel.NORMAL:
            self._trigger_recovery()

    def _trigger_degradation(self, level: SafetyLevel):
        """触发降级"""
        try:
            if level == SafetyLevel.EMERGENCY:
                # 紧急状态：完全禁用知识库功能
                self.logger.critical("触发紧急降级：完全禁用知识库功能")
                if self.kb_config_manager:
                    self.kb_config_manager.disable_knowledge_base()
            elif level == SafetyLevel.DEGRADED:
                # 降级状态：部分禁用功能
                self.logger.warning("触发降级：限制知识库功能")
                if self.kb_config_manager:
                    self.kb_config_manager.get_config()
                    # 可以在这里实现部分功能禁用
                    # 例如：禁用复杂查询，只保留基本功能

            # 执行降级回调
            for callback in self.degradation_callbacks:
                try:
                    callback(level)
                except Exception as e:
                    self.logger.error(f"降级回调执行失败: {e}")

            # 启动自动恢复检查
            self._start_recovery_timer()

        except Exception as e:
            self.logger.error(f"降级处理失败: {e}")

    def _trigger_recovery(self):
        """触发恢复"""
        try:
            self.logger.info("触发系统恢复")

            # 重新启用知识库功能
            if self.kb_config_manager:
                self.kb_config_manager.enable_knowledge_base()

            # 执行恢复回调
            for callback in self.recovery_callbacks:
                try:
                    callback()
                except Exception as e:
                    self.logger.error(f"恢复回调执行失败: {e}")

            # 停止恢复定时器
            self._stop_recovery_timer()

        except Exception as e:
            self.logger.error(f"恢复处理失败: {e}")

    def _can_recover(self) -> bool:
        """检查是否可以恢复"""
        if not self.config["auto_recovery"]["enabled"]:
            return False

        # 检查最近的错误情况
        now = datetime.now()
        window = self.config["time_windows"]["error_rate_window"]
        recent_errors = [
            error for error in self.error_history
            if (now - error.timestamp).total_seconds() <= window
        ]

        # 如果最近没有错误，可以尝试恢复
        return len(recent_errors) == 0

    def _start_recovery_timer(self):
        """启动恢复定时器"""
        if self._recovery_timer:
            self._recovery_timer.cancel()

        interval = self.config["time_windows"]["recovery_check_interval"]
        self._recovery_timer = threading.Timer(interval, self._check_recovery)
        self._recovery_timer.start()

    def _stop_recovery_timer(self):
        """停止恢复定时器"""
        if self._recovery_timer:
            self._recovery_timer.cancel()
            self._recovery_timer = None

    def _check_recovery(self):
        """检查恢复条件"""
        if self._can_recover():
            self._change_safety_level(SafetyLevel.NORMAL)
        else:
            # 继续检查
            self._start_recovery_timer()

    def force_degradation(self, level: SafetyLevel, reason: str):
        """强制降级"""
        self.logger.warning(f"强制降级到 {level.value}: {reason}")
        self.record_error("FORCED_DEGRADATION", reason, severity="CRITICAL")
        self._change_safety_level(level)

    def force_recovery(self, reason: str):
        """强制恢复"""
        self.logger.info(f"强制恢复: {reason}")
        self._change_safety_level(SafetyLevel.NORMAL)

    def get_safety_status(self) -> Dict[str, Any]:
        """获取安全状态"""
        with self._lock:
            recent_errors = self._get_recent_errors(300)  # 最近5分钟的错误

            return {
                "safety_level": self.metrics.safety_level.value,
                "metrics": {
                    "total_requests": self.metrics.total_requests,
                    "successful_requests": self.metrics.successful_requests,
                    "failed_requests": self.metrics.failed_requests,
                    "error_rate": round(self.metrics.error_rate, 4),
                    "avg_response_time": round(self.metrics.avg_response_time, 3),
                    "consecutive_errors": self.metrics.consecutive_errors,
                    "last_error_time": self.metrics.last_error_time.isoformat() if self.metrics.last_error_time else None
                },
                "recent_errors": len(recent_errors),
                "kb_enabled": self.kb_config_manager.is_knowledge_base_enabled() if self.kb_config_manager else False,
                "auto_recovery_enabled": self.config["auto_recovery"]["enabled"]
            }

    def _get_recent_errors(self, seconds: int) -> List[ErrorRecord]:
        """获取最近的错误记录"""
        now = datetime.now()
        return [
            error for error in self.error_history
            if (now - error.timestamp).total_seconds() <= seconds
        ]

    def reset_metrics(self):
        """重置指标"""
        with self._lock:
            self.metrics = SafetyMetrics()
            self.error_history.clear()
            self.logger.info("安全指标已重置")

    def add_degradation_callback(self, callback: Callable):
        """添加降级回调"""
        self.degradation_callbacks.append(callback)

    def add_recovery_callback(self, callback: Callable):
        """添加恢复回调"""
        self.recovery_callbacks.append(callback)

    def __del__(self):
        """析构函数"""
        self._stop_recovery_timer()


# 全局安全管理器实例
_safety_manager = None
_safety_manager_lock = threading.Lock()


def get_safety_manager() -> SafetyManager:
    """获取全局安全管理器实例"""
    global _safety_manager

    if _safety_manager is None:
        with _safety_manager_lock:
            if _safety_manager is None:
                _safety_manager = SafetyManager()

    return _safety_manager
