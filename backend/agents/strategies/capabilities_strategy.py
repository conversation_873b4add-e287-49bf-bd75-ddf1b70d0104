"""
能力介绍策略实现

处理用户对AI助手能力的询问，包括：
- 功能能力询问（你能做什么、有什么功能等）
- 服务范围询问（支持哪些服务、提供什么帮助等）
- 技能展示请求（展示能力、介绍特长等）

优先级: 6 (中)
支持意图: ask_capabilities
"""

import logging
from typing import List
import random

from ..decision_types import DecisionStrategy, AnalyzedContext, DecisionResult, create_decision_result
from ..unified_state_manager import ConversationState
from backend.config.unified_config_loader import get_unified_config


class CapabilitiesStrategy(DecisionStrategy):
    """能力介绍策略实现"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 🔧 [配置化重构] 获取统一配置和关键词加载器
        self.config = get_unified_config()
        from backend.config.keywords_loader import get_keywords_loader
        self.keywords_loader = get_keywords_loader()

        # 🔧 [配置化重构] 从关键词配置加载能力询问关键词
        self.capability_keywords = (
            self.keywords_loader.get_keywords("strategy_keywords.capabilities_strategy.capability_inquiry") or
            ["能做什么", "有什么功能", "功能", "能力", "会什么", "支持", "提供", "帮助"]  # 最小化硬编码安全网
        )

        # 🔧 [配置化重构] 从配置加载能力介绍模板
        self.capability_descriptions = {
            "core_abilities": (
                self.config.get_config_value("strategy_templates.capabilities_strategy.templates.core_abilities") or
                ["我是一个智能AI助手，主要能力包括："]  # 最小化硬编码安全网
            ),
            "specific_services": (
                self.config.get_config_value("strategy_templates.capabilities_strategy.templates.specific_services") or
                ["📋 需求收集与分析 - 帮助您梳理项目需求和想法"]  # 最小化硬编码安全网
            ),
            "interaction_features": (
                self.config.get_config_value("strategy_templates.capabilities_strategy.templates.interaction_features") or
                ["✨ 智能对话 - 理解您的需求并提供针对性回复"]  # 最小化硬编码安全网
            )
        }

        # 🔧 [配置化重构] 从配置加载详细能力说明
        self.detailed_explanations = (
            self.config.get_config_value("strategy_templates.capabilities_strategy.detailed_explanations") or
            {  # 最小化硬编码安全网
                "requirement_collection": "我可以通过对话了解您的具体需求，包括项目类型、功能要求、预算范围、时间计划等。",
                "consultation": "基于丰富的知识库，我能为您提供专业的建议和方案。"
            }
        )

        # 🔧 [配置化重构] 从配置加载使用场景示例
        self.use_cases = (
            self.config.get_config_value("strategy_templates.capabilities_strategy.templates.use_cases") or
            ["🏢 企业项目规划 - 帮助企业梳理数字化转型需求"]  # 最小化硬编码安全网
        )

    @property
    def name(self) -> str:
        return "capabilities_strategy"

    @property
    def supported_intents(self) -> List[str]:
        return ["ask_capabilities"]

    @property
    def priority(self) -> int:
        return 6  # 中等优先级

    async def can_handle(self, context: AnalyzedContext) -> bool:
        """
        判断是否能处理当前上下文

        Args:
            context: 分析后的上下文

        Returns:
            bool: 是否能处理
        """
        # 1. 基于意图判断
        if context.intent == "ask_capabilities":
            return True

        # 2. 基于关键词判断
        message_lower = context.message.lower()
        for keyword in self.capability_keywords:
            if keyword in message_lower:
                return True

        # 3. 基于特定询问模式判断
        capability_patterns = [
            "你.*能.*", "你.*会.*", "你.*可以.*",
            "有.*功能", "支持.*", "提供.*服务",
            "介绍.*能力", "展示.*功能"
        ]

        import re
        for pattern in capability_patterns:
            if re.search(pattern, context.message):
                return True

        return False

    async def execute(self, context: AnalyzedContext) -> DecisionResult:
        """
        执行能力介绍策略

        Args:
            context: 分析后的上下文

        Returns:
            DecisionResult: 决策结果
        """
        try:
            # 分析询问类型
            inquiry_type = self._analyze_inquiry_type(context)

            # 确定介绍详细程度
            detail_level = self._determine_detail_level(context)

            # 生成能力介绍内容
            response_content = self._generate_capability_response(inquiry_type, detail_level)

            # 计算置信度
            confidence = await self.calculate_confidence(context)

            # 创建决策结果
            result = create_decision_result(
                action="explain_capabilities",
                confidence=confidence,
                intent=context.intent,
                emotion=context.emotion,
                response_template=response_content,
                next_state=ConversationState.IDLE,
                strategy_name=self.name,
                metadata={
                    "inquiry_type": inquiry_type,
                    "detail_level": detail_level,
                    "response_length": len(response_content),
                    "includes_examples": "examples" in inquiry_type
                }
            )

            self.logger.info(f"能力介绍策略执行成功: type={inquiry_type}, detail={detail_level}")
            return result

        except Exception as e:
            self.logger.error(f"能力介绍策略执行失败: {e}", exc_info=True)

            # 返回基础能力介绍
            return create_decision_result(
                action="explain_capabilities",
                confidence=0.7,
                intent="ask_capabilities",
                emotion="neutral",
                response_template="我是AI助手，可以帮助您处理各种需求，包括项目咨询、信息查询、方案建议等。有什么具体需要了解的吗？",
                next_state=ConversationState.IDLE,
                strategy_name=self.name,
                metadata={"error": str(e), "fallback": True}
            )

    async def calculate_confidence(self, context: AnalyzedContext) -> float:
        """
        计算处理置信度

        Args:
            context: 分析后的上下文

        Returns:
            float: 置信度 (0.0-1.0)
        """
        confidence_factors = []

        # 1. 意图匹配度
        if context.intent == "ask_capabilities":
            confidence_factors.append(0.95)
        else:
            confidence_factors.append(0.4)

        # 2. 关键词匹配度
        message_lower = context.message.lower()
        keyword_matches = sum(1 for keyword in self.capability_keywords if keyword in message_lower)
        if keyword_matches > 0:
            confidence_factors.append(min(0.9, 0.5 + keyword_matches * 0.2))
        else:
            confidence_factors.append(0.3)

        # 3. 询问模式匹配
        if self._has_capability_inquiry_pattern(context.message):
            confidence_factors.append(0.8)
        else:
            confidence_factors.append(0.4)

        # 4. 消息特征分析
        if self._is_direct_capability_question(context.message):
            confidence_factors.append(0.9)
        else:
            confidence_factors.append(0.5)

        return sum(confidence_factors) / len(confidence_factors)

    def _analyze_inquiry_type(self, context: AnalyzedContext) -> str:
        """分析询问类型"""
        message_lower = context.message.lower()

        # 检查是否询问具体功能
        if any(word in message_lower for word in ["功能", "能做", "会做"]):
            return "specific_functions"

        # 检查是否询问服务范围
        if any(word in message_lower for word in ["服务", "支持", "帮助"]):
            return "service_scope"

        # 检查是否要求展示能力
        if any(word in message_lower for word in ["展示", "介绍", "说明"]):
            return "capability_demo"

        # 检查是否询问使用场景
        if any(word in message_lower for word in ["场景", "用途", "应用"]):
            return "use_cases"

        return "general_inquiry"

    def _determine_detail_level(self, context: AnalyzedContext) -> str:
        """确定介绍详细程度"""
        message = context.message

        # 基于消息长度
        if len(message) > 30:
            return "detailed"
        elif len(message) > 15:
            return "moderate"
        else:
            return "brief"

    def _generate_capability_response(self, inquiry_type: str, detail_level: str) -> str:
        """生成能力介绍回复"""
        response_parts = []

        # 添加开场介绍
        intro = random.choice(self.capability_descriptions["core_abilities"])
        response_parts.append(intro)

        # 根据询问类型添加具体内容
        if inquiry_type == "specific_functions":
            response_parts.extend(self.capability_descriptions["specific_services"])
        elif inquiry_type == "service_scope":
            response_parts.extend(self.capability_descriptions["interaction_features"])
        elif inquiry_type == "use_cases":
            response_parts.append("\n🎯 主要应用场景：")
            response_parts.extend(self.use_cases)
        else:
            # 通用介绍
            response_parts.extend(self.capability_descriptions["specific_services"][:3])

        # 根据详细程度调整内容
        if detail_level == "detailed":
            response_parts.append("\n💡 详细说明：")
            response_parts.append(random.choice(list(self.detailed_explanations.values())))

        # 添加结尾引导
        closing_guidance = get_unified_config().get_config_value("message_templates.capabilities_strategy.closing_guidance")
        response_parts.append(closing_guidance)

        return "\n".join(response_parts)

    def _has_capability_inquiry_pattern(self, message: str) -> bool:
        """检查是否有能力询问模式"""
        import re

        # 🔧 [配置化重构] 从配置加载询问模式正则表达式
        patterns = (
            self.config.get_config_value("strategy_templates.capabilities_strategy.patterns.inquiry_patterns") or
            ["你.*能.*", "你.*会.*", "你.*可以.*", "有.*功能"]  # 最小化硬编码安全网
        )

        for pattern in patterns:
            if re.search(pattern, message):
                return True
        return False

    def _is_direct_capability_question(self, message: str) -> bool:
        """判断是否为直接的能力询问"""
        # 🔧 [配置化重构] 使用配置化的关键词进行直接询问判断
        direct_questions = self.capability_keywords

        message_lower = message.lower()
        for question in direct_questions:
            if question in message_lower:
                return True

        return False

    async def calculate_context_match(self, context: AnalyzedContext) -> float:
        """
        计算与上下文的匹配度

        Args:
            context: 分析后的上下文

        Returns:
            float: 匹配度 (0.0-1.0)
        """
        if context.intent in self.supported_intents:
            return 1.0

        # 基于关键词匹配度
        message_lower = context.message.lower()
        keyword_matches = sum(1 for keyword in self.capability_keywords if keyword in message_lower)

        if keyword_matches > 0:
            return min(0.9, keyword_matches * 0.3)

        # 基于询问模式匹配度
        if self._has_capability_inquiry_pattern(context.message):
            return 0.7

        return 0.0


# 导出策略类
__all__ = ['CapabilitiesStrategy']
