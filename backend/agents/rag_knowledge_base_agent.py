"""
RAG知识库代理

基于检索增强生成（RAG）的知识库问答代理，集成ChromaDB向量检索
和LLM生成，提供上下文感知的知识库问答功能。
"""

import logging
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import chromadb
import os

# 强制禁用ChromaDB遥测
os.environ['ANONYMIZED_TELEMETRY'] = 'False'
os.environ['CHROMA_TELEMETRY_DISABLED'] = 'True'

from backend.config.knowledge_base_config import get_knowledge_base_config_manager
from backend.utils.safety_manager import get_safety_manager
from backend.config.unified_config_loader import get_unified_config


@dataclass
class RAGQueryResult:
    """RAG查询结果"""
    success: bool
    answer: str
    sources: List[Dict[str, Any]]
    processing_info: Dict[str, Any]
    error: Optional[str] = None


class RAGKnowledgeBaseAgent:
    """
    RAG知识库代理

    基于现有的KnowledgeBaseAgent进行扩展，实现：
    - 向量检索和生成式回答
    - 集成ChromaDB向量数据库
    - 提供上下文感知的知识库问答
    - 支持角色过滤和元数据查询
    """

    def __init__(self, llm_service: Any, agent_name: str = "rag_knowledge_base_agent"):
        """
        初始化RAG知识库代理

        Args:
            llm_service: LLM服务实例
            agent_name: 代理名称
        """
        self.llm_service = llm_service
        self.agent_name = agent_name
        self.logger = logging.getLogger(__name__)

        # 获取配置
        self.config_manager = get_knowledge_base_config_manager()
        self.config = self.config_manager.get_config()

        # 获取安全管理器
        self.safety_manager = get_safety_manager()

        # ChromaDB配置
        self.chroma_db_path = self.config.chroma_db.get('path', 'backend/data/chroma_db')
        self.collection_name = self.config.chroma_db.get('collection_name', 'hybrid_knowledge_base')
        self.embedding_model = self.config.chroma_db.get('embedding_model', 'moka-ai/m3e-base')

        # 检索配置
        self.retrieval_config = self.config.retrieval
        self.top_k = self.retrieval_config.get('top_k', 5)
        self.similarity_threshold = self.retrieval_config.get('similarity_threshold', 0.7)
        self.max_context_length = self.retrieval_config.get('max_context_length', 4000)

        # 安全配置
        self.safety_config = self.config.safety
        self.timeout_seconds = self.safety_config.get('timeout_seconds', 10)
        self.max_retry_attempts = self.safety_config.get('max_retry_attempts', 3)

        # 初始化ChromaDB客户端
        self._chroma_client = None
        self._collection = None
        self._initialize_chroma_client()

    def _repair_chromadb_if_needed(self):
        """
        检查并修复ChromaDB数据库结构问题（仅检查主库，一次性修复，忽略备份文件）
        """
        import sqlite3
        import shutil
        from pathlib import Path

        try:
            chroma_path = Path(self.chroma_db_path)
            if not chroma_path.exists():
                self.logger.info("ChromaDB目录不存在，将自动创建")
                return

            # 哨兵文件：修复过则跳过
            sentinel = chroma_path / ".repaired"
            if sentinel.exists():
                self.logger.info("ChromaDB已修复过，跳过结构检查")
                return

            # 仅检查主库文件，忽略备份/错误备份
            candidates = [
                chroma_path / "chroma.sqlite3",
                chroma_path / "chroma.sqlite"
            ]
            db_files = [f for f in candidates if f.exists()]

            for db_file in db_files:
                try:
                    # 检查数据库结构
                    conn = sqlite3.connect(str(db_file))
                    cursor = conn.cursor()

                    # 检查collections表是否存在（ChromaDB 1.0.17标准schema不包含topic列）
                    cursor.execute("PRAGMA table_info(collections)")
                    columns = cursor.fetchall()
                    column_names = [col[1] for col in columns]

                    # 检查必要的标准列是否存在
                    required_columns = ['id', 'name', 'dimension', 'database_id']
                    missing_columns = [col for col in required_columns if col not in column_names]

                    if missing_columns:
                        self.logger.warning(f"检测到ChromaDB结构问题，缺少必要列: {missing_columns}, 文件: {db_file}")
                        conn.close()

                        # 备份损坏的数据库（只备份一次）
                        backup_file = db_file.with_suffix(f"{db_file.suffix}.backup")
                        if not backup_file.exists():
                            shutil.copy2(db_file, backup_file)
                            self.logger.info(f"已备份损坏的数据库到: {backup_file}")

                        # 删除损坏的数据库文件
                        db_file.unlink()
                        self.logger.info(f"已删除损坏的数据库文件: {db_file}")
                    else:
                        conn.close()

                except Exception as e:
                    self.logger.warning(f"检查数据库文件失败 {db_file}: {e}")
                    # 不对无法识别的文件做链式备份，直接记录并跳过
                    continue

            # 修复流程执行过，写入哨兵
            sentinel.touch()

        except Exception as e:
            self.logger.error(f"修复ChromaDB数据库失败: {e}")
            # 不再整体移动目录，避免破坏现有数据；仅记录错误

    def _force_cleanup_and_recreate(self):
        """
        强制清理并重新创建ChromaDB客户端
        """
        import shutil
        from pathlib import Path

        try:
            self.logger.info("开始强制清理ChromaDB...")

            # 关闭现有客户端
            if self._chroma_client:
                try:
                    self._chroma_client = None
                except:
                    pass

            self._collection = None

            # 删除ChromaDB目录
            chroma_path = Path(self.chroma_db_path)
            if chroma_path.exists():
                backup_path = chroma_path.with_suffix(f".force_cleanup_backup")
                if backup_path.exists():
                    shutil.rmtree(backup_path)
                shutil.move(chroma_path, backup_path)
                self.logger.info(f"已备份ChromaDB目录到: {backup_path}")

            # 重新创建客户端
            settings = chromadb.Settings(
                anonymized_telemetry=False,
                allow_reset=True,
                is_persistent=True
            )

            self._chroma_client = chromadb.PersistentClient(
                path=self.chroma_db_path,
                settings=settings
            )

            self.logger.info("强制清理完成，已重新创建ChromaDB客户端")

        except Exception as e:
            self.logger.error(f"强制清理失败: {e}")

    def _initialize_chroma_client(self) -> bool:
        """
        初始化ChromaDB客户端

        Returns:
            bool: 初始化是否成功
        """
        try:
            # 解析绝对路径以避免cwd差异导致的多实例/找不到集合
            from pathlib import Path
            db_path = Path(self.chroma_db_path)
            if not db_path.is_absolute():
                project_root = Path(__file__).resolve().parents[2]
                db_path = (project_root / db_path).resolve()

            self.logger.info(f"初始化ChromaDB客户端: {db_path}")

            # 🔧 修复：在初始化前检查并修复ChromaDB结构问题
            self._repair_chromadb_if_needed()

            # 创建ChromaDB客户端，明确设置参数以避免冲突
            settings = chromadb.Settings(
                anonymized_telemetry=False,
                allow_reset=True,
                is_persistent=True
            )

            self._chroma_client = chromadb.PersistentClient(
                path=str(db_path),
                settings=settings
            )

            # 设置嵌入函数
            from chromadb.utils import embedding_functions
            embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
                model_name=self.embedding_model
            )

            # 尝试连接现有集合，如果不存在则自动创建
            try:
                self._collection = self._chroma_client.get_collection(
                    name=self.collection_name,
                    embedding_function=embedding_function
                )
                self.logger.info(f"成功连接到现有集合: {self.collection_name}")
            except Exception as e:
                self.logger.warning(f"集合不存在或连接失败: {e}，尝试自动创建集合")
                try:
                    # 自动创建集合
                    self._collection = self._chroma_client.create_collection(
                        name=self.collection_name,
                        embedding_function=embedding_function,
                        metadata={"hnsw:space": "cosine"}
                    )
                    self.logger.info(f"成功创建新集合: {self.collection_name}")
                except Exception as create_error:
                    self.logger.error(
                        "创建ChromaDB集合失败: %s。请先运行 scripts/reset_knowledge_base.py 进行初始化或检查路径配置。",
                        create_error,
                    )
                    self._chroma_client = None
                    self._collection = None
                    return False

            return True

        except Exception as e:
            self.logger.error(f"初始化ChromaDB客户端失败: {e}")
            self._chroma_client = None
            self._collection = None
            return False

    async def query(self, question: str, context: Optional[Dict[str, Any]] = None) -> RAGQueryResult:
        """
        执行RAG查询

        Args:
            question: 用户问题
            context: 查询上下文（包含会话信息、角色过滤等）

        Returns:
            RAGQueryResult: 查询结果
        """
        start_time = time.time()

        try:
            self.logger.info(f"开始RAG查询: '{question}'")

            # 检查知识库功能是否启用
            if not self.config_manager.is_enabled():
                return RAGQueryResult(
                    success=False,
                    answer="知识库查询功能当前未启用",
                    sources=[],
                    processing_info={},
                    error="feature_disabled"
                )

            # 检查ChromaDB连接
            if not self._collection:
                if not self._initialize_chroma_client():
                    return RAGQueryResult(
                        success=False,
                        answer="知识库连接失败，请稍后再试",
                        sources=[],
                        processing_info={},
                        error="connection_failed"
                    )

            # 执行向量检索
            retrieval_start = time.time()
            relevant_docs = self._retrieve_relevant_docs(question, context)
            retrieval_time = time.time() - retrieval_start

            if not relevant_docs:
                return RAGQueryResult(
                    success=True,
                    answer=get_unified_config().get_config_value("message_templates.error.no_knowledge_found"),
                    sources=[],
                    processing_info={
                        "method": "rag_retrieval",
                        "retrieval_time": retrieval_time,
                        "generation_time": 0,
                        "total_time": time.time() - start_time,
                        "retrieved_docs": 0
                    }
                )

            # 生成答案
            generation_start = time.time()
            answer = await self._generate_answer(question, relevant_docs, context)
            generation_time = time.time() - generation_start

            # 构建源信息
            sources = self._build_sources_info(relevant_docs)

            total_time = time.time() - start_time

            self.logger.info(f"RAG查询完成，耗时: {total_time:.2f}s")

            # 记录成功请求
            self.safety_manager.record_request(
                success=True,
                response_time=total_time,
                component="rag_knowledge_base",
                context={"question_length": len(question), "docs_found": len(relevant_docs)}
            )

            return RAGQueryResult(
                success=True,
                answer=answer,
                sources=sources,
                processing_info={
                    "method": "rag_retrieval",
                    "retrieval_time": retrieval_time,
                    "generation_time": generation_time,
                    "total_time": total_time,
                    "retrieved_docs": len(relevant_docs)
                }
            )

        except Exception as e:
            self.logger.error(f"RAG查询失败: {e}", exc_info=True)

            # 记录错误
            self.safety_manager.record_error(
                error_type="RAG_QUERY_ERROR",
                error_message=str(e),
                component="rag_knowledge_base",
                severity="ERROR",
                context={"question": question[:100]}
            )

            return RAGQueryResult(
                success=False,
                answer=get_unified_config().get_config_value("message_templates.error.system_problem"),
                sources=[],
                processing_info={
                    "total_time": time.time() - start_time
                },
                error=str(e)
            )

    def _retrieve_relevant_docs(self, question: str, context: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        检索相关文档

        Args:
            question: 用户问题
            context: 查询上下文

        Returns:
            List[Dict[str, Any]]: 相关文档列表
        """
        try:
            # 构建查询参数
            query_params = {
                "query_texts": [question],
                "n_results": self.top_k
            }

            # 添加角色过滤
            if context and context.get('role_filter'):
                role = context['role_filter']
                if role in self.config.role_filters.get('available_roles', []):
                    query_params["where"] = {"role": {"$eq": role}}
                    self.logger.info(f"应用角色过滤: {role}")

            # 执行查询
            results = self._collection.query(**query_params)

            # 处理结果
            relevant_docs = []
            if results['documents'][0]:
                for i, (doc, metadata, distance) in enumerate(zip(
                    results['documents'][0],
                    results['metadatas'][0],
                    results['distances'][0]
                )):
                    similarity_score = 1 - distance

                    # 应用相似度阈值
                    if similarity_score >= self.similarity_threshold:
                        relevant_docs.append({
                            "content": doc,
                            "metadata": metadata,
                            "similarity_score": similarity_score,
                            "rank": i + 1
                        })

            self.logger.info(f"检索到 {len(relevant_docs)} 个相关文档")
            return relevant_docs

        except Exception as e:
            self.logger.error(f"文档检索失败: {e}")
            return []

    async def _generate_answer(self, question: str, relevant_docs: List[Dict[str, Any]],
                             context: Optional[Dict[str, Any]] = None) -> str:
        """
        基于检索文档生成答案

        Args:
            question: 用户问题
            relevant_docs: 相关文档列表
            context: 查询上下文

        Returns:
            str: 生成的答案
        """
        try:
            # 构建上下文
            context_text = self._build_context_text(relevant_docs)

            # 构建提示词
            prompt = self._build_rag_prompt(question, context_text, context)

            # 调用LLM生成答案
            response = await self.llm_service.call_llm(
                messages=[{"role": "user", "content": prompt}],
                agent_name=self.agent_name,
                scenario="knowledge_base_rag"
            )

            answer = response.get("content", "").strip()

            if not answer:
                return get_unified_config().get_config_value("message_templates.error.cannot_generate_reply")

            return answer

        except Exception as e:
            self.logger.error(f"答案生成失败: {e}")
            # 降级处理：返回检索到的原始文档片段
            return self._fallback_answer(relevant_docs)

    def _build_context_text(self, relevant_docs: List[Dict[str, Any]]) -> str:
        """
        构建上下文文本

        Args:
            relevant_docs: 相关文档列表

        Returns:
            str: 构建的上下文文本
        """
        context_parts = []
        current_length = 0

        for doc in relevant_docs:
            content = doc.get("content", "") or ""
            metadata = doc.get("metadata") or {}
            # 兼容不同导入脚本使用的字段
            source = (
                metadata.get("source_file")
                or metadata.get("source")
                or metadata.get("source_path")
                or metadata.get("source_name")
                or "unknown"
            )
            role = metadata.get("role") or "unknown"

            # 添加文档信息
            doc_info = f"[文档: {source} - 角色: {role}]\n{content}\n"

            # 检查长度限制
            if current_length + len(doc_info) > self.max_context_length:
                break

            context_parts.append(doc_info)
            current_length += len(doc_info)

        return "\n".join(context_parts)

    def _build_rag_prompt(self, question: str, context_text: str,
                         context: Optional[Dict[str, Any]] = None) -> str:
        """
        构建RAG提示词

        Args:
            question: 用户问题
            context_text: 上下文文本
            context: 查询上下文

        Returns:
            str: 构建的提示词
        """
        role_info = ""
        if context and context.get('role_filter'):
            role_map = {"company": "雇主", "developer": "自由职业者"}
            role_name = role_map.get(context['role_filter'], context['role_filter'])
            role_info = f"注意：用户的身份是{role_name}，请针对这个身份提供相关建议。"

        prompt = f"""你是一个专业的知识库助手，请基于以下文档内容回答用户的问题。

{role_info}

相关文档内容：
{context_text}

用户问题：{question}

请根据上述文档内容，为用户提供准确、有用的回答。要求：
1. 回答要基于文档内容，不要编造信息
2. 如果文档中没有直接答案，请说明并提供相关建议
3. 回答要简洁明了，重点突出
4. 如果涉及具体操作步骤，请按顺序列出
5. 保持友好和专业的语调

回答："""

        return prompt

    def _fallback_answer(self, relevant_docs: List[Dict[str, Any]]) -> str:
        """
        降级处理：返回检索到的原始文档片段

        Args:
            relevant_docs: 相关文档列表

        Returns:
            str: 降级答案
        """
        if not relevant_docs:
            return get_unified_config().get_config_value("message_templates.error.no_information")

        answer_parts = ["根据知识库中的相关信息：\n"]

        for i, doc in enumerate(relevant_docs[:2], 1):  # 只取前2个最相关的
            raw_content = doc.get("content", "") or ""
            content = raw_content[:200] + "..." if len(raw_content) > 200 else raw_content
            metadata = doc.get("metadata") or {}
            source = (
                metadata.get("source_file")
                or metadata.get("source")
                or metadata.get("source_path")
                or metadata.get("source_name")
                or "unknown"
            )

            answer_parts.append(f"{i}. 来源：{source}")
            answer_parts.append(f"   {content}\n")

        answer_parts.append("如需更详细信息，请告诉我您的具体需求。")

        return "\n".join(answer_parts)

    def _build_sources_info(self, relevant_docs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        构建源信息

        Args:
            relevant_docs: 相关文档列表

        Returns:
            List[Dict[str, Any]]: 源信息列表
        """
        sources = []

        for doc in relevant_docs:
            metadata = doc.get("metadata") or {}
            raw_content = doc.get("content", "") or ""
            similarity = doc.get("similarity_score")
            source = (
                metadata.get("source_file")
                or metadata.get("source")
                or metadata.get("source_path")
                or metadata.get("source_name")
                or "unknown"
            )
            role = metadata.get("role") or "unknown"
            snippet = raw_content[:150] + "..." if len(raw_content) > 150 else raw_content

            sources.append({
                "document": f"{role}/{source}",
                "relevance_score": similarity,
                "snippet": snippet,
                "role": role,
                "chunk_info": {
                    "chunk_index": metadata.get('chunk_index', 0),
                    "total_chunks": metadata.get('total_chunks', 1)
                }
            })

        return sources

    def get_collection_stats(self) -> Dict[str, Any]:
        """
        获取集合统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            if not self._collection:
                return {"error": "集合未初始化"}

            count = self._collection.count()

            # 获取角色分布
            role_stats = {}
            try:
                # 查询所有文档的角色信息
                all_results = self._collection.get(include=["metadatas"])
                if all_results and all_results.get('metadatas'):
                    for metadata in all_results['metadatas']:
                        role = metadata.get('role', 'unknown')
                        role_stats[role] = role_stats.get(role, 0) + 1
            except Exception as e:
                self.logger.warning(f"获取角色统计失败: {e}")

            return {
                "total_documents": count,
                "collection_name": self.collection_name,
                "role_distribution": role_stats,
                "embedding_model": self.embedding_model
            }

        except Exception as e:
            self.logger.error(f"获取集合统计失败: {e}")
            return {"error": str(e)}

    def health_check(self) -> Dict[str, Any]:
        """
        健康检查

        Returns:
            Dict[str, Any]: 健康检查结果
        """
        health_status = {
            "healthy": True,
            "checks": {},
            "timestamp": time.time()
        }

        # 检查配置
        try:
            config_enabled = self.config_manager.is_feature_enabled('rag_query')
            health_status["checks"]["config"] = {
                "status": "ok" if config_enabled else "disabled",
                "enabled": config_enabled
            }
        except Exception as e:
            health_status["checks"]["config"] = {
                "status": "error",
                "error": str(e)
            }
            health_status["healthy"] = False

        # 检查ChromaDB连接
        try:
            if self._collection:
                count = self._collection.count()
                health_status["checks"]["chromadb"] = {
                    "status": "ok",
                    "document_count": count
                }
            else:
                health_status["checks"]["chromadb"] = {
                    "status": "error",
                    "error": "集合未初始化"
                }
                health_status["healthy"] = False
        except Exception as e:
            health_status["checks"]["chromadb"] = {
                "status": "error",
                "error": str(e)
            }
            health_status["healthy"] = False

        # 检查LLM服务
        try:
            if self.llm_service:
                health_status["checks"]["llm_service"] = {
                    "status": "ok",
                    "service_available": True
                }
            else:
                health_status["checks"]["llm_service"] = {
                    "status": "error",
                    "error": "LLM服务未初始化"
                }
                health_status["healthy"] = False
        except Exception as e:
            health_status["checks"]["llm_service"] = {
                "status": "error",
                "error": str(e)
            }
            health_status["healthy"] = False

        return health_status
