# -*- coding: utf-8 -*-
"""
消息回复管理器 - 统一管理所有类型的消息回复
版本: v1.0
作者: AI Assistant
创建时间: 2025-06-20

功能:
1. 统一管理静态消息模板
2. 处理动态LLM生成的回复
3. 支持配置化的回复策略
4. 提供回复效果监控和分析
5. 支持多语言和个性化回复
"""

import logging
from typing import Dict, Any, List
from enum import Enum
from backend.utils.prompt_loader import PromptLoader


class MessageType(Enum):
    """消息类型枚举"""
    STATIC = "static"           # 静态模板消息
    DYNAMIC = "dynamic"         # 动态LLM生成消息
    HYBRID = "hybrid"           # 混合类型（模板+动态内容）
    FALLBACK = "fallback"       # 回退消息


class ReplyCategory(Enum):
    """回复类别枚举"""
    GREETING = "greeting"                    # 问候
    CONFIRMATION = "confirmation"            # 确认
    ERROR = "error"                         # 错误
    GUIDANCE = "guidance"                   # 引导
    CLARIFICATION = "clarification"         # 澄清
    EMPATHY = "empathy"                     # 共情
    COMPLETION = "completion"               # 完成
    SYSTEM = "system"                       # 系统
    CUSTOM = "custom"                       # 自定义


class MessageReplyManager:
    """消息回复管理器"""

    def __init__(self, llm_client=None, config_path: str = None):
        """
        初始化消息回复管理器

        Args:
            llm_client: LLM客户端实例
            config_path: 配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        self.llm_client = llm_client
        self.prompt_loader = PromptLoader()

        # 跟踪初始化
        from backend.config.unified_config_loader import _init_tracker
        _init_tracker.track_init("MessageReplyManager")

        # 加载配置
        self.config = self._load_config(config_path)

        # 静态消息模板缓存
        self._static_templates = {}

        # 动态回复生成器配置
        self._dynamic_generators = {}

        # 回复效果统计
        self._reply_stats = {
            "total_replies": 0,
            "success_count": 0,
            "fallback_count": 0,
            "error_count": 0,
            "category_stats": {},
            "type_stats": {}
        }

        # 初始化模板和生成器
        self._initialize_templates()
        self._initialize_generators()

        # 检查是否为重复初始化
        init_repeat_count = getattr(self, '_init_count', 0) + 1
        setattr(self, '_init_count', init_repeat_count)

        if init_repeat_count > 1:
            self.logger.info(f"消息回复管理器初始化完成 [重复 {init_repeat_count - 1} 次]")
        else:
            self.logger.info("消息回复管理器初始化完成")

    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """加载配置文件 - 使用新的统一配置"""
        try:
            # 使用统一的配置管理器
            from backend.config.unified_config_loader import get_unified_config

            # 获取统一消息配置
            message_config = get_unified_config().get_message_config()

            if message_config:
                # 检查是否为重复加载
                repeat_count = getattr(self, '_config_load_count', 0) + 1
                setattr(self, '_config_load_count', repeat_count)

                if repeat_count > 1:
                    self.logger.info(f"已加载统一消息配置: unified_config.yaml [重复 {repeat_count - 1} 次]")
                else:
                    self.logger.info("已加载统一消息配置: unified_config.yaml")
                return message_config
            else:
                self.logger.warning("统一消息配置不存在，使用默认配置")
                return self._get_default_config()

        except Exception as e:
            self.logger.error(f"加载统一消息配置失败: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "default_language": "zh-CN",
            "fallback_enabled": True,
            "llm_timeout": 10,
            "max_retry_attempts": 3,
            "enable_analytics": True,
            "template_version": "1.0",
            "supported_languages": ["zh-CN", "en-US"],
            "reply_categories": {
                category.value: {
                    "enabled": True,
                    "priority": 1,
                    "fallback_template": f"default_{category.value}_message"
                } for category in ReplyCategory
            }
        }

    def _initialize_templates(self):
        """初始化静态消息模板 - 从统一配置文件加载"""
        try:
            # 从统一配置文件加载模板
            templates_config = self.config.get("templates", {})
            self._static_templates = {}

            # 将配置文件中的模板转换为简单的键值对
            for key, template_config in templates_config.items():
                if isinstance(template_config, dict) and "content" in template_config:
                    self._static_templates[key] = template_config["content"]
                else:
                    # 兼容旧格式
                    self._static_templates[key] = str(template_config)

            # 检查是否为重复加载
            template_repeat_count = getattr(self, '_template_load_count', 0) + 1
            setattr(self, '_template_load_count', template_repeat_count)

            if template_repeat_count > 1:
                self.logger.info(f"已从统一配置文件加载 {len(self._static_templates)} 个静态消息模板 [重复 {template_repeat_count - 1} 次]")
            else:
                self.logger.info(f"已从统一配置文件加载 {len(self._static_templates)} 个静态消息模板")

        except Exception as e:
            self.logger.error(f"加载静态模板失败: {e}")
            # 回退到空字典，让系统使用其他回退机制
            self._static_templates = {}

    def _initialize_generators(self):
        """初始化动态回复生成器配置 - 从统一配置文件加载"""
        try:
            # 从统一配置文件加载生成器配置
            generators_config = self.config.get("generators", {})
            self._dynamic_generators = {}

            # 转换配置格式
            for key, generator_config in generators_config.items():
                if isinstance(generator_config, dict):
                    self._dynamic_generators[key] = generator_config

            # 如果配置为空，使用默认配置
            if not self._dynamic_generators:
                self._dynamic_generators = {
                    "empathy_generator": {
                        "agent_name": "empathy_generator",
                        "temperature": 0.7,
                        "max_tokens": 200,
                        "fallback_template": "clarification_request"
                    },
                    "greeting_generator": {
                        "agent_name": "greeting_generator",
                        "temperature": 0.7,
                        "max_tokens": 150,
                        "fallback_template": "greeting_basic"
                    }
                }

            # 检查是否为重复加载
            generator_repeat_count = getattr(self, '_generator_load_count', 0) + 1
            setattr(self, '_generator_load_count', generator_repeat_count)

            if generator_repeat_count > 1:
                self.logger.info(f"已从统一配置文件加载 {len(self._dynamic_generators)} 个动态回复生成器 [重复 {generator_repeat_count - 1} 次]")
            else:
                self.logger.info(f"已从统一配置文件加载 {len(self._dynamic_generators)} 个动态回复生成器")

        except Exception as e:
            self.logger.error(f"加载动态生成器配置失败: {e}")
            self._dynamic_generators = {}

    async def get_reply(
        self,
        reply_key: str,
        message_type: MessageType = MessageType.STATIC,
        context: Dict[str, Any] = None,
        **kwargs
    ) -> str:
        """
        获取回复消息

        Args:
            reply_key: 回复键值
            message_type: 消息类型
            context: 上下文信息
            **kwargs: 其他参数

        Returns:
            str: 回复消息
        """
        try:
            self._reply_stats["total_replies"] += 1

            if message_type == MessageType.STATIC:
                reply = await self._get_static_reply(reply_key, context, **kwargs)
            elif message_type == MessageType.DYNAMIC:
                reply = await self._get_dynamic_reply(reply_key, context, **kwargs)
            elif message_type == MessageType.HYBRID:
                reply = await self._get_hybrid_reply(reply_key, context, **kwargs)
            else:
                reply = await self._get_fallback_reply(reply_key, context, **kwargs)

            if reply:
                self._reply_stats["success_count"] += 1
                self._update_category_stats(reply_key, "success")
                self._update_type_stats(message_type.value, "success")
                return reply
            else:
                return await self._get_fallback_reply(reply_key, context, **kwargs)

        except Exception as e:
            self.logger.error(f"获取回复失败: {e}", exc_info=True)
            self._reply_stats["error_count"] += 1
            return await self._get_fallback_reply(reply_key, context, **kwargs)

    async def _get_static_reply(self, reply_key: str, context: Dict[str, Any] = None, **kwargs) -> str:
        """获取静态回复"""
        template = self._static_templates.get(reply_key)
        if not template:
            self.logger.warning(f"未找到静态模板: {reply_key}")
            return None

        # 如果模板包含占位符，进行格式化
        if context and '{' in template:
            try:
                return template.format(**context, **kwargs)
            except KeyError as e:
                self.logger.warning(f"模板格式化失败，缺少参数: {e}")
                return template

        return template

    async def _get_dynamic_reply(self, reply_key: str, context: Dict[str, Any] = None, **kwargs) -> str:
        """获取动态LLM生成的回复"""
        if not self.llm_client:
            self.logger.warning("LLM客户端未初始化，无法生成动态回复")
            return None

        generator_config = self._dynamic_generators.get(reply_key)
        if not generator_config:
            self.logger.warning(f"未找到动态生成器配置: {reply_key}")
            return None

        try:
            # 构建提示词
            prompt_instruction = context.get('prompt_instruction') if context else None
            if not prompt_instruction:
                self.logger.warning(f"动态回复缺少prompt_instruction: {reply_key}")
                return None

            # 构建完整提示
            user_message = context.get('message', '') if context else ''
            full_prompt = f"{prompt_instruction}\n\n用户输入: {user_message}" if user_message else prompt_instruction

            # 调用LLM
            response = await self.llm_client.call_llm(
                messages=[{"role": "user", "content": full_prompt}],
                agent_name=generator_config.get("agent_name"),
                temperature=generator_config.get("temperature", 0.7),
                max_tokens=generator_config.get("max_tokens", 200)
            )

            generated_reply = response.get("content", "").strip()
            if generated_reply:
                self.logger.info(f"LLM成功生成动态回复: {reply_key}")
                return generated_reply
            else:
                self.logger.warning(f"LLM未能生成有效回复: {reply_key}")
                return None

        except Exception as e:
            self.logger.error(f"动态回复生成失败: {e}", exc_info=True)
            return None

    async def _get_hybrid_reply(self, reply_key: str, context: Dict[str, Any] = None, **kwargs) -> str:
        """获取混合类型回复（模板+动态内容）"""
        # 先尝试获取静态模板作为基础
        base_template = await self._get_static_reply(reply_key, context, **kwargs)
        if not base_template:
            return await self._get_dynamic_reply(reply_key, context, **kwargs)

        # 如果需要动态增强，调用LLM
        if context and context.get('enhance_with_llm', False):
            dynamic_part = await self._get_dynamic_reply(f"{reply_key}_dynamic", context, **kwargs)
            if dynamic_part:
                return f"{base_template}\n\n{dynamic_part}"

        return base_template

    async def _get_fallback_reply(self, reply_key: str, context: Dict[str, Any] = None, **kwargs) -> str:
        """获取回退回复"""
        self._reply_stats["fallback_count"] += 1

        # 尝试从配置中获取回退模板
        fallback_key = f"{reply_key}_fallback"
        fallback_reply = self._static_templates.get(fallback_key)

        if fallback_reply:
            return fallback_reply

        # 使用通用回退消息
        from backend.config.unified_config_loader import get_unified_config
        config = get_unified_config()
        fallback_message = config.get_config_value("message_templates.message_reply_manager.unknown_action_fallback")
        default_fallback = config.get_message_template("guidance.default_requirement_prompt", fallback_message)
        return self._static_templates.get("unknown_action", default_fallback)

    def _update_category_stats(self, reply_key: str, status: str):
        """更新类别统计"""
        if reply_key not in self._reply_stats["category_stats"]:
            self._reply_stats["category_stats"][reply_key] = {"success": 0, "failure": 0}

        self._reply_stats["category_stats"][reply_key][status] = \
            self._reply_stats["category_stats"][reply_key].get(status, 0) + 1

    def _update_type_stats(self, message_type: str, status: str):
        """更新类型统计"""
        if message_type not in self._reply_stats["type_stats"]:
            self._reply_stats["type_stats"][message_type] = {"success": 0, "failure": 0}

        self._reply_stats["type_stats"][message_type][status] = \
            self._reply_stats["type_stats"][message_type].get(status, 0) + 1

    def get_reply_stats(self) -> Dict[str, Any]:
        """获取回复统计信息"""
        return {
            **self._reply_stats,
            "success_rate": self._reply_stats["success_count"] / max(self._reply_stats["total_replies"], 1),
            "fallback_rate": self._reply_stats["fallback_count"] / max(self._reply_stats["total_replies"], 1),
            "error_rate": self._reply_stats["error_count"] / max(self._reply_stats["total_replies"], 1)
        }

    def add_static_template(self, key: str, template: str, category: ReplyCategory = ReplyCategory.CUSTOM):
        """添加静态模板"""
        self._static_templates[key] = template
        self.logger.info(f"已添加静态模板: {key}")

    def add_dynamic_generator(self, key: str, config: Dict[str, Any]):
        """添加动态生成器配置"""
        self._dynamic_generators[key] = config
        self.logger.info(f"已添加动态生成器: {key}")

    def list_available_templates(self) -> Dict[str, List[str]]:
        """列出可用的模板"""
        return {
            "static_templates": list(self._static_templates.keys()),
            "dynamic_generators": list(self._dynamic_generators.keys())
        }
