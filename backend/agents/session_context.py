"""
会话上下文管理模块

提供无状态Agent架构所需的会话上下文数据结构和管理功能。
这个模块是实现多用户并发安全的核心组件。
"""

from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional
from enum import Enum, auto
from datetime import datetime
from backend.config.unified_config_loader import get_unified_config


class ConversationState(Enum):
    """对话状态枚举"""
    IDLE = auto()              # 1 空闲状态
    PROCESSING_INTENT = auto() # 2 处理意图
    COLLECTING_INFO = auto()   # 3 收集需求信息
    DOCUMENTING = auto()       # 4 文档生成与修改


@dataclass
class SessionContext:
    """
    会话上下文数据结构

    封装所有与特定用户会话相关的状态信息，替代Agent实例变量。
    这是实现无状态Agent架构的核心数据结构。

    设计原则：
    - 不可变性：尽可能使用不可变数据结构
    - 完整性：包含处理用户请求所需的所有状态信息
    - 可序列化：支持持久化到数据库或缓存
    - 类型安全：使用类型注解确保数据一致性
    """

    # === 基础会话信息 ===
    session_id: str
    user_id: str
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    # === 对话状态信息 ===
    current_state: ConversationState = ConversationState.IDLE

    # === 领域和分类信息 ===
    current_domain: Optional[str] = None
    current_category: Optional[str] = None

    # === 核心业务信息 ===
    problem_statement: Optional[str] = None  # 用户核心问题陈述

    # === 关注点和状态信息 ===
    current_focus_points_definitions: List[Dict[str, Any]] = field(default_factory=list)

    # === 分类结果缓存（用于单次请求内的数据传递） ===
    latest_domain_result: Optional[Dict[str, Any]] = None
    latest_category_result: Optional[Dict[str, Any]] = None

    # === 文档相关信息 ===
    current_document: Optional[Dict[str, Any]] = None

    # === 信息采集上下文 ===
    collection_context: Dict[str, Any] = field(default_factory=dict)

    # === 计数器和统计信息 ===
    pending_count: int = 0
    max_pending_attempts: int = 3

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，用于序列化"""
        return {
            'session_id': self.session_id,
            'user_id': self.user_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'current_state': self.current_state.name,
            'current_domain': self.current_domain,
            'current_category': self.current_category,
            'problem_statement': self.problem_statement,
            'current_focus_points_definitions': self.current_focus_points_definitions,
            'latest_domain_result': self.latest_domain_result,
            'latest_category_result': self.latest_category_result,
            'current_document': self.current_document,
            'collection_context': self.collection_context,
            'pending_count': self.pending_count,
            'max_pending_attempts': self.max_pending_attempts,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SessionContext':
        """从字典创建SessionContext实例"""
        # 处理日期时间字段
        created_at = datetime.fromisoformat(data.get('created_at', datetime.now().isoformat()))
        updated_at = datetime.fromisoformat(data.get('updated_at', datetime.now().isoformat()))

        # 处理枚举字段
        state_name = data.get('current_state', 'IDLE')
        current_state = getattr(ConversationState, state_name, ConversationState.IDLE)

        return cls(
            session_id=data['session_id'],
            user_id=data['user_id'],
            created_at=created_at,
            updated_at=updated_at,
            current_state=current_state,
            current_domain=data.get('current_domain'),
            current_category=data.get('current_category'),
            problem_statement=data.get('problem_statement'),
            current_focus_points_definitions=data.get('current_focus_points_definitions', []),
            latest_domain_result=data.get('latest_domain_result'),
            latest_category_result=data.get('latest_category_result'),
            current_document=data.get('current_document'),
            collection_context=data.get('collection_context', {}),
            pending_count=data.get('pending_count', 0),
            max_pending_attempts=data.get('max_pending_attempts', 3),
        )

    def update_timestamp(self) -> 'SessionContext':
        """更新时间戳，返回新的实例（保持不可变性）"""
        return self.__class__(
            session_id=self.session_id,
            user_id=self.user_id,
            created_at=self.created_at,
            updated_at=datetime.now(),
            current_state=self.current_state,
            current_domain=self.current_domain,
            current_category=self.current_category,
            problem_statement=self.problem_statement,
            current_focus_points_definitions=self.current_focus_points_definitions,
            latest_domain_result=self.latest_domain_result,
            latest_category_result=self.latest_category_result,
            current_document=self.current_document,
            collection_context=self.collection_context,
            pending_count=self.pending_count,
            max_pending_attempts=self.max_pending_attempts,
        )

    def with_state(self, new_state: ConversationState) -> 'SessionContext':
        """返回具有新状态的SessionContext实例"""
        return self.__class__(
            session_id=self.session_id,
            user_id=self.user_id,
            created_at=self.created_at,
            updated_at=datetime.now(),
            current_state=new_state,
            current_domain=self.current_domain,
            current_category=self.current_category,
            problem_statement=self.problem_statement,
            current_focus_points_definitions=self.current_focus_points_definitions,
            latest_domain_result=self.latest_domain_result,
            latest_category_result=self.latest_category_result,
            current_document=self.current_document,
            collection_context=self.collection_context,
            pending_count=self.pending_count,
            max_pending_attempts=self.max_pending_attempts,
        )

    def with_domain_category(self, domain: str, category: str) -> 'SessionContext':
        """返回具有新领域和分类的SessionContext实例"""
        return self.__class__(
            session_id=self.session_id,
            user_id=self.user_id,
            created_at=self.created_at,
            updated_at=datetime.now(),
            current_state=self.current_state,
            current_domain=domain,
            current_category=category,
            problem_statement=self.problem_statement,
            current_focus_points_definitions=self.current_focus_points_definitions,
            latest_domain_result=self.latest_domain_result,
            latest_category_result=self.latest_category_result,
            current_document=self.current_document,
            collection_context=self.collection_context,
            pending_count=self.pending_count,
            max_pending_attempts=self.max_pending_attempts,
        )


class SessionContextManager:
    """
    会话上下文管理器

    负责SessionContext的创建、加载、保存和管理。
    提供数据库和缓存的抽象接口。
    """

    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.config = get_unified_config()

    async def load_session_context(self, session_id: str, user_id: str) -> SessionContext:
        """
        从数据库加载会话上下文

        Args:
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            SessionContext: 会话上下文实例
        """
        # 创建基础上下文
        context = SessionContext(session_id=session_id, user_id=user_id)

        try:
            # 从数据库加载领域和分类信息
            query = self.config.get_config_value("database.queries.conversations.get_domain_category")
            domain_category_result = await self.db_manager.get_record(
                query,
                (session_id, user_id)
            )

            if domain_category_result:
                context = context.with_domain_category(
                    domain_category_result.get('domain_id'),
                    domain_category_result.get('category_id')
                )

                # 加载对应的关注点定义
                focus_points_definitions = await self._load_focus_points_definitions(
                    domain_category_result.get('category_id')
                )
                if focus_points_definitions:
                    context = SessionContext(
                        session_id=context.session_id,
                        user_id=context.user_id,
                        created_at=context.created_at,
                        updated_at=datetime.now(),
                        current_state=context.current_state,
                        current_domain=context.current_domain,
                        current_category=context.current_category,
                        problem_statement=context.problem_statement,
                        current_focus_points_definitions=focus_points_definitions,
                        latest_domain_result=context.latest_domain_result,
                        latest_category_result=context.latest_category_result,
                        current_document=context.current_document,
                        collection_context=context.collection_context,
                        pending_count=context.pending_count,
                        max_pending_attempts=context.max_pending_attempts,
                    )

            # 加载问题陈述（第一条用户消息）
            query = self.config.get_config_value("database.queries.messages.get_first_user_message")
            first_message_result = await self.db_manager.get_record(
                query,
                (session_id, user_id)
            )

            if first_message_result:
                context = SessionContext(
                    session_id=context.session_id,
                    user_id=context.user_id,
                    created_at=context.created_at,
                    updated_at=datetime.now(),
                    current_state=context.current_state,
                    current_domain=context.current_domain,
                    current_category=context.current_category,
                    problem_statement=first_message_result.get('content'),
                    current_focus_points_definitions=context.current_focus_points_definitions,
                    latest_domain_result=context.latest_domain_result,
                    latest_category_result=context.latest_category_result,
                    current_document=context.current_document,
                    collection_context=context.collection_context,
                    pending_count=context.pending_count,
                    max_pending_attempts=context.max_pending_attempts,
                )

            # 确定当前对话状态
            current_state = await self._determine_conversation_state(session_id, user_id)
            if current_state != context.current_state.name:
                state_enum = getattr(ConversationState, current_state, ConversationState.IDLE)
                context = context.with_state(state_enum)

        except Exception as e:
            # 如果加载失败，返回默认上下文
            print(f"加载会话上下文失败: {e}")

        return context

    async def save_session_context(self, session_context: SessionContext) -> bool:
        """
        保存会话上下文到数据库

        Args:
            session_context: 要保存的会话上下文

        Returns:
            bool: 保存是否成功
        """
        try:
            # 更新会话的基本信息
            query = self.config.get_config_value("database.queries.conversations.update_domain")
            await self.db_manager.execute_update(
                query,
                (
                    session_context.current_domain,
                    session_context.current_category,
                    datetime.now().isoformat(),
                    session_context.session_id,
                    session_context.user_id
                )
            )

            # 保存会话状态到专门的状态表（如果存在）
            try:
                query = self.config.get_config_value("database.queries.session_states.insert_or_replace")
                await self.db_manager.execute_update(
                    query,
                    (
                        session_context.session_id,
                        session_context.user_id,
                        session_context.current_state.name,
                        datetime.now().isoformat()
                    )
                )
            except Exception as e:
                # 如果session_states表不存在，忽略这个错误（但不记录到错误日志）
                if "no such table: session_states" not in str(e):
                    # 只有非表不存在的错误才记录
                    print(f"保存会话状态失败: {e}")

            return True

        except Exception as e:
            print(f"保存会话上下文失败: {e}")
            return False

    async def _load_focus_points_definitions(self, category_id: str) -> List[Dict[str, Any]]:
        """
        从数据库加载关注点定义

        Args:
            category_id: 类别ID

        Returns:
            List[Dict[str, Any]]: 关注点定义列表
        """
        try:
            focus_points = await self.db_manager.get_records(
                """
                SELECT focus_id as id, name, description, priority, example, required
                FROM focus_point_definitions
                WHERE category_id = ?
                ORDER BY priority, focus_id
                """,
                (category_id,)
            )

            # 转换为字典列表
            result = []
            for point in focus_points:
                result.append({
                    'id': point['id'],
                    'name': point['name'],
                    'description': point['description'],
                    'priority': point['priority'],
                    'example': point['example'],
                    'required': bool(point['required'])
                })

            return result

        except Exception as e:
            print(f"加载关注点定义失败: {e}")
            return []

    async def _determine_conversation_state(self, session_id: str, user_id: str) -> str:
        """确定当前对话状态"""
        try:
            # 检查是否有未完成的文档
            query = self.config.get_config_value("database.queries.documents.get_draft_document")
            doc_result = await self.db_manager.get_record(
                query,
                (session_id, user_id)
            )

            if doc_result:
                return "DOCUMENTING"

            # 检查是否有领域和分类信息
            query = self.config.get_config_value("database.queries.conversations.get_domain_category")
            domain_result = await self.db_manager.get_record(
                query,
                (session_id, user_id)
            )

            if domain_result and domain_result.get('domain_id'):
                return "COLLECTING_INFO"

            return "IDLE"

        except Exception:
            return "IDLE"
