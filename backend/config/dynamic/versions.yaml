# ============================================================================
# 动态版本配置
# ============================================================================
# 文件说明：系统版本和功能开关的动态配置，支持运行时切换
# 维护责任：DevOps团队 + 产品团队
# 更新频率：中频（版本发布和功能开关时）
# 依赖关系：被版本管理和功能开关模块依赖
# 特点：支持热更新，用于灰度发布和A/B测试
# ============================================================================

# 系统版本信息
system_version:
  # 当前版本
  current: "3.0.0"
  
  # 版本历史
  history:
    - version: "3.0.0"
      release_date: "2025-08-18"
      description: "配置管理架构升级版本"
      status: "current"
      
    - version: "2.1.0"
      release_date: "2025-07-15"
      description: "多用户并发优化版本"
      status: "deprecated"
      
    - version: "2.0.0"
      release_date: "2025-06-01"
      description: "Handler模式重构版本"
      status: "deprecated"
      
  # 版本兼容性
  compatibility:
    min_supported: "2.0.0"
    api_version: "v1"
    schema_version: "1.0"

# 功能开关配置
feature_flags:
  # 核心功能开关
  core_features:
    # 关键词加速
    keyword_acceleration:
      enabled: true
      rollout_percentage: 100
      description: "关键词匹配加速功能"
      
    # 语义匹配
    semantic_matching:
      enabled: true
      rollout_percentage: 100
      description: "语义匹配功能"
      
    # 意图识别
    intent_recognition:
      enabled: true
      rollout_percentage: 100
      description: "意图识别功能"
      
    # 文档生成
    document_generation:
      enabled: true
      rollout_percentage: 100
      description: "需求文档生成功能"
      
  # 实验性功能开关
  experimental_features:
    # 多轮对话优化
    multi_turn_optimization:
      enabled: false
      rollout_percentage: 0
      description: "多轮对话上下文优化"
      target_users: ["beta_testers"]
      
    # 个性化响应
    personalized_response:
      enabled: false
      rollout_percentage: 5
      description: "个性化响应生成"
      target_users: ["premium_users"]
      
    # 情感分析
    emotion_analysis:
      enabled: false
      rollout_percentage: 0
      description: "用户情感分析功能"
      
    # 智能建议
    smart_suggestions:
      enabled: false
      rollout_percentage: 10
      description: "智能需求建议功能"
      
  # 性能优化功能
  performance_features:
    # 响应缓存
    response_caching:
      enabled: true
      rollout_percentage: 100
      description: "响应结果缓存"
      
    # 预加载优化
    preloading:
      enabled: true
      rollout_percentage: 100
      description: "配置预加载优化"
      
    # 批处理
    batch_processing:
      enabled: false
      rollout_percentage: 0
      description: "批量请求处理"
      
  # 安全功能开关
  security_features:
    # 请求频率限制
    rate_limiting:
      enabled: true
      rollout_percentage: 100
      description: "API请求频率限制"
      
    # 内容过滤
    content_filtering:
      enabled: true
      rollout_percentage: 100
      description: "用户输入内容过滤"
      
    # 审计日志
    audit_logging:
      enabled: true
      rollout_percentage: 100
      description: "操作审计日志记录"

# A/B测试配置
ab_testing:
  # 测试开关
  enabled: false
  
  # 当前测试
  active_tests:
    # 响应模板测试
    response_template_test:
      enabled: false
      traffic_split: 50  # 50%流量参与测试
      variants:
        control: "original_templates"
        treatment: "optimized_templates"
      metrics: ["user_satisfaction", "completion_rate"]
      
    # LLM模型对比测试
    llm_model_test:
      enabled: false
      traffic_split: 20
      variants:
        control: "deepseek-chat"
        treatment: "doubao-1.5-Lite"
      metrics: ["response_time", "accuracy", "cost"]

# 灰度发布配置
gradual_rollout:
  # 灰度策略
  strategy: "percentage_based"  # percentage_based, user_based, region_based
  
  # 当前灰度功能
  active_rollouts:
    # 新配置架构灰度
    new_config_architecture:
      enabled: true
      rollout_percentage: 10
      target_groups: ["internal_users", "beta_testers"]
      success_criteria:
        error_rate: 0.01
        response_time: 2.0
        user_satisfaction: 0.8
        
    # 增强语义匹配
    enhanced_semantic_matching:
      enabled: false
      rollout_percentage: 0
      target_groups: []

# 环境配置
environment_config:
  # 当前环境
  current_environment: "development"  # development, staging, production
  
  # 环境特定配置
  environments:
    development:
      debug_mode: true
      feature_flags_override: true
      experimental_features_enabled: true
      
    staging:
      debug_mode: false
      feature_flags_override: true
      experimental_features_enabled: true
      
    production:
      debug_mode: false
      feature_flags_override: false
      experimental_features_enabled: false

# 配置热更新
hot_update:
  # 热更新开关
  enabled: true
  
  # 更新检查间隔
  check_interval: 60  # 1分钟
  
  # 更新来源
  sources:
    - "file_system"
    - "remote_config_service"
    - "database"
    
  # 更新验证
  validation:
    enabled: true
    rollback_on_error: true
    health_check_after_update: true

# 监控和告警
monitoring:
  # 版本监控
  version_tracking: true
  
  # 功能使用监控
  feature_usage_tracking: true
  
  # A/B测试监控
  ab_test_monitoring: true
  
  # 告警配置
  alerts:
    # 功能异常告警
    feature_error_rate_threshold: 0.05
    
    # 版本兼容性告警
    compatibility_check: true
    
    # 灰度发布告警
    rollout_success_rate_threshold: 0.95

# 回滚配置
rollback:
  # 自动回滚
  auto_rollback:
    enabled: true
    error_rate_threshold: 0.1
    response_time_threshold: 5.0
    
  # 手动回滚
  manual_rollback:
    enabled: true
    require_approval: true
    
  # 回滚历史
  history_retention: 30  # 天

# 用户分组配置
user_groups:
  # 内部用户
  internal_users:
    description: "内部测试用户"
    feature_access: "all"
    
  # Beta测试用户
  beta_testers:
    description: "Beta功能测试用户"
    feature_access: "experimental"
    
  # 高级用户
  premium_users:
    description: "付费高级用户"
    feature_access: "premium"
    
  # 普通用户
  regular_users:
    description: "普通用户"
    feature_access: "standard"

# ============================================================================
# 版本管理指南
# ============================================================================
# 1. 版本发布流程：
#    - 更新版本号和发布说明
#    - 配置功能开关和灰度策略
#    - 监控关键指标
#    - 根据反馈调整或回滚
#
# 2. 功能开关使用：
#    - 新功能默认关闭，逐步开放
#    - 设置合理的目标用户群体
#    - 监控功能使用情况和错误率
#
# 3. A/B测试最佳实践：
#    - 明确测试目标和成功指标
#    - 确保样本量足够
#    - 控制测试时间和范围
# ============================================================================

# ============================================================================
# 配置元数据
# ============================================================================
_metadata:
  file_version: "1.0"
  created_date: "2025-08-18"
  last_modified: "2025-08-18"
  schema_version: "1.0"
  config_type: "dynamic_versions"
  priority: 1  # 在优先级配置中的位置（最高优先级）
